#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import httpx

async def test_price_monitoring_api():
    """测试价格监控API"""
    print('🔧 测试价格监控API:')
    
    # 测试参数
    store_id = '93289212-7943-48ab-8092-e8eb7f663677'
    period = 'week'
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # 调用价格监控API
            url = f"http://localhost:8000/api/v1/project/{store_id}/super-dashboard/price-monitoring"
            params = {
                'store_id': store_id,
                'period': period
            }
            
            print(f'请求URL: {url}')
            print(f'请求参数: {params}')
            
            response = await client.get(url, params=params)
            print(f'响应状态码: {response.status_code}')
            
            if response.status_code == 200:
                data = response.json()
                print('✅ 价格监控API调用成功')
                print(f'响应数据: {data}')
            else:
                print(f'❌ 价格监控API调用失败: {response.text}')
                
        except Exception as e:
            import traceback
            print(f'❌ 价格监控API调用异常: {e}')
            print(f'详细错误信息: {traceback.format_exc()}')

if __name__ == "__main__":
    asyncio.run(test_price_monitoring_api())
