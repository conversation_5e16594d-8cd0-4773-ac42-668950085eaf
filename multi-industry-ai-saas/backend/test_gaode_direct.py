#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import httpx

async def test_gaode_direct():
    """直接测试高德地理编码API"""
    print('🔧 直接测试高德地理编码API:')
    
    api_key = "94ed64a48ce623e65b18134befb65750"
    test_address = "北京市朝阳区建国路1号"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            url = "https://restapi.amap.com/v3/geocode/geo"
            params = {
                "key": api_key,
                "address": test_address,
                "output": "json"
            }
            
            print(f'请求URL: {url}')
            print(f'请求参数: {params}')
            
            response = await client.get(url, params=params)
            print(f'响应状态码: {response.status_code}')
            
            if response.status_code == 200:
                data = response.json()
                print('✅ 高德API调用成功')
                print(f'响应数据: {data}')
                
                if data.get("status") == "1" and data.get("geocodes"):
                    geocode = data["geocodes"][0]
                    location = geocode["location"].split(",")
                    print(f'解析结果:')
                    print(f'  经度: {location[0]}')
                    print(f'  纬度: {location[1]}')
                    print(f'  格式化地址: {geocode.get("formatted_address", test_address)}')
                else:
                    print(f'❌ 地址解析失败: {data.get("info", "未知错误")}')
            else:
                print(f'❌ 高德API调用失败: {response.text}')
                
        except Exception as e:
            import traceback
            print(f'❌ 高德API调用异常: {e}')
            print(f'详细错误信息: {traceback.format_exc()}')

if __name__ == "__main__":
    asyncio.run(test_gaode_direct())
