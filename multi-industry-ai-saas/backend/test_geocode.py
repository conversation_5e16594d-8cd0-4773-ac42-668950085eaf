#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import httpx

async def test_geocode_api():
    """测试地址解析API"""
    print('🔧 测试地址解析API:')
    
    # 测试地址
    test_address = "北京市朝阳区建国路1号"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # 调用地址解析API
            url = "http://localhost:8000/api/v1/project/93289212-7943-48ab-8092-e8eb7f663677/basic/stores/geocode"
            data = {"address": test_address}

            # 添加认证头（如果需要的话）
            headers = {
                "Content-Type": "application/json",
                # "Authorization": "Bearer your_token_here"  # 如果需要认证
            }

            print(f'请求URL: {url}')
            print(f'请求数据: {data}')

            response = await client.post(url, json=data, headers=headers)
            if response.status_code == 200:
                result = response.json()
                print('✅ 地址解析API调用成功')
                print(f'地址: {test_address}')
                print(f'结果: {result}')
            else:
                print(f'❌ 地址解析API调用失败，状态码: {response.status_code}')
                print(f'错误信息: {response.text}')
        except Exception as e:
            import traceback
            print(f'❌ 地址解析API调用异常: {e}')
            print(f'详细错误信息: {traceback.format_exc()}')

if __name__ == "__main__":
    asyncio.run(test_geocode_api())
