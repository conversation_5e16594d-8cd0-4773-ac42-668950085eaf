#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint, Index
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from db.database import Base

class DingTalkWorkspace(Base):
    """钉钉协作空间映射"""
    __tablename__ = "dingtalk_workspaces"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 钉钉协作空间信息
    dingtalk_workspace_id = Column(String(100), nullable=False)         # 钉钉协作空间ID
    name = Column(String(200), nullable=False)                          # 协作空间名称
    description = Column(Text, nullable=True)                           # 协作空间描述
    
    # 空间统计信息
    space_count = Column(Integer, default=0)                            # 知识库数量
    
    # 同步状态
    sync_status = Column(String(20), default="active")                  # 同步状态（active, paused, error）
    last_sync_at = Column(DateTime(timezone=True), nullable=True)       # 最后同步时间
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_workspaces")
    project = relationship("Project", backref="dingtalk_workspaces")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'dingtalk_workspace_id', name='uq_dingtalk_workspace'),
        Index('idx_dingtalk_workspace_sync', 'sync_status', 'last_sync_at'),
    )

    def __repr__(self):
        return f"<DingTalkWorkspace(id={self.id}, name='{self.name}', workspace_id='{self.dingtalk_workspace_id}')>"


class DingTalkSpace(Base):
    """钉钉知识库映射"""
    __tablename__ = "dingtalk_spaces"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 钉钉知识库信息
    dingtalk_space_id = Column(String(100), nullable=False)         # 钉钉知识库ID
    dingtalk_workspace_id = Column(String(100), nullable=False)     # 钉钉工作空间ID
    space_name = Column(String(200), nullable=False)                # 知识库名称
    space_description = Column(Text, nullable=True)                 # 知识库描述
    
    # 项目空间关联
    project_space_id = Column(UUID(as_uuid=True), nullable=True)    # 关联的项目空间ID
    auto_sync = Column(Boolean, default=True)                       # 是否自动同步
    sync_config = Column(JSON, nullable=True)                       # 同步配置
    
    # 统计信息
    doc_count = Column(Integer, default=0)                          # 文档数量
    last_sync_doc_count = Column(Integer, default=0)                # 最后同步的文档数量
    
    # 同步状态
    sync_status = Column(String(20), default="active")              # 同步状态（active, paused, error）
    last_sync_at = Column(DateTime(timezone=True), nullable=True)   # 最后同步时间
    next_sync_at = Column(DateTime(timezone=True), nullable=True)   # 下次同步时间
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_spaces")
    project = relationship("Project", backref="dingtalk_spaces")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'dingtalk_space_id', name='uq_dingtalk_space'),
    )

    def __repr__(self):
        return f"<DingTalkSpace(id={self.id}, name='{self.space_name}', space_id='{self.dingtalk_space_id}')>"


class DingTalkUserMapping(Base):
    """钉钉用户映射"""
    __tablename__ = "dingtalk_user_mappings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=True)  # 修改为可空，允许先创建映射后绑定用户
    dingtalk_user_id = Column(String(100), nullable=False)
    dingtalk_union_id = Column(String(100), nullable=True)  # 钉钉unionId
    dingtalk_name = Column(String(100), nullable=True)
    dingtalk_mobile = Column(String(20), nullable=True)
    dingtalk_email = Column(String(100), nullable=True)
    dingtalk_department = Column(String(200), nullable=True)  # 部门信息
    dingtalk_position = Column(String(100), nullable=True)    # 职位信息
    dingtalk_avatar = Column(String(500), nullable=True)      # 头像URL
    dingtalk_dept_id = Column(String(50), nullable=True)      # 钉钉部门ID
    dingtalk_dept_name = Column(String(200), nullable=True)   # 钉钉部门名称
    dingtalk_dept_path = Column(String(500), nullable=True)   # 部门路径（如：公司/技术部/开发组）
    dingtalk_job_number = Column(String(50), nullable=True)   # 工号
    dingtalk_title = Column(String(100), nullable=True)       # 职务
    dingtalk_work_place = Column(String(200), nullable=True)  # 工作地点
    dingtalk_remark = Column(String(500), nullable=True)      # 备注
    dingtalk_order_in_dept = Column(Integer, nullable=True)   # 在部门中的排序
    dingtalk_extension = Column(JSON, nullable=True)          # 扩展字段（JSON格式）
    is_active = Column(Boolean, default=True)                 # 是否活跃
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_user_mappings")
    project = relationship("Project", backref="dingtalk_user_mappings")
    user = relationship("User", backref="dingtalk_user_mapping")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'dingtalk_user_id', name='uq_dingtalk_user_mapping'),
    )

    def __repr__(self):
        return f"<DingTalkUserMapping(id={self.id}, user_id='{self.user_id}', dingtalk_user_id='{self.dingtalk_user_id}')>"


class DingTalkDepartment(Base):
    """钉钉部门结构"""
    __tablename__ = "dingtalk_departments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    dept_id = Column(String(50), nullable=False)              # 钉钉部门ID
    name = Column(String(200), nullable=False)                # 部门名称
    parent_id = Column(String(50), nullable=True)             # 父部门ID
    dept_path = Column(String(500), nullable=True)            # 部门路径
    level = Column(Integer, default=1)                        # 部门层级
    order = Column(Integer, default=0)                        # 排序
    member_count = Column(Integer, default=0)                 # 成员数量
    dept_manager_userid_list = Column(ARRAY(String), default=list)  # 部门主管用户ID列表
    dept_hiding = Column(Boolean, default=False)              # 是否隐藏部门
    outer_dept = Column(Boolean, default=False)               # 是否是根部门
    source_identifier = Column(String(100), nullable=True)    # 来源标识
    ext = Column(JSON, nullable=True)                         # 扩展字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_departments")
    project = relationship("Project", backref="dingtalk_departments")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'dept_id', name='uq_dingtalk_department'),
    )

    def __repr__(self):
        return f"<DingTalkDepartment(id={self.id}, name='{self.name}', dept_id='{self.dept_id}')>"


class DingTalkTodoTask(Base):
    """钉钉待办任务映射"""
    __tablename__ = "dingtalk_todo_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    system_task_id = Column(UUID(as_uuid=True), nullable=True)           # 系统任务ID（可关联到任务系统）
    dingtalk_task_id = Column(String(100), nullable=False)               # 钉钉待办任务ID
    dingtalk_record_id = Column(String(100), nullable=True)              # 钉钉待办记录ID（兼容字段）
    dingtalk_user_id = Column(String(100), nullable=False)               # 钉钉用户ID
    subject = Column(String(500), nullable=False)                        # 任务主题
    title = Column(String(500), nullable=True)                           # 任务标题（兼容字段）
    description = Column(Text, nullable=True)                            # 任务描述
    url = Column(String(1000), nullable=True)                            # 任务详情URL
    priority = Column(String(20), nullable=True)                         # 任务优先级
    due_time = Column(DateTime(timezone=True), nullable=True)            # 截止时间
    due_date = Column(DateTime(timezone=True), nullable=True)            # 截止日期（兼容字段）
    is_done = Column(Boolean, default=False)                             # 是否完成
    created_time = Column(DateTime(timezone=True), nullable=True)        # 钉钉创建时间
    raw_data = Column(JSON, nullable=True)                               # 原始钉钉数据
    status = Column(String(20), default="pending")                       # 任务状态（pending, completed, cancelled）
    dingtalk_status = Column(Integer, default=0)                         # 钉钉任务状态（0-未完成，1-已完成）
    sync_status = Column(String(20), default="synced")                   # 同步状态（synced, pending, failed）
    last_sync_at = Column(DateTime(timezone=True), nullable=True)        # 最后同步时间
    form_data = Column(JSON, nullable=True)                              # 钉钉表单数据
    task_metadata = Column(JSON, nullable=True)                          # 任务元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_todo_tasks")
    project = relationship("Project", backref="dingtalk_todo_tasks")
    user = relationship("User", backref="dingtalk_todo_tasks")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'dingtalk_record_id', name='uq_dingtalk_todo_task'),
        Index('idx_dingtalk_todo_user_status', 'user_id', 'status'),
        Index('idx_dingtalk_todo_sync_status', 'sync_status', 'last_sync_at'),
    )

    def __repr__(self):
        return f"<DingTalkTodoTask(id={self.id}, title='{self.title}', status='{self.status}')>"


class DingTalkDocument(Base):
    """钉钉文档映射"""
    __tablename__ = "dingtalk_documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    
    # 钉钉文档信息
    dingtalk_node_id = Column(String(100), nullable=False)          # 钉钉节点ID
    dingtalk_doc_id = Column(String(100), nullable=True)            # 钉钉文档ID（兼容字段）
    dingtalk_space_id = Column(String(100), nullable=True)          # 钉钉知识库ID
    dingtalk_workspace_id = Column(String(100), nullable=True)      # 钉钉工作空间ID
    
    # 文档基本信息
    title = Column(String(500), nullable=False)                     # 文档标题
    content = Column(Text, nullable=True)                           # 文档内容
    content_format = Column(String(20), default="markdown")         # 内容格式
    node_type = Column(String(50), nullable=True)                   # 节点类型（doc, sheet, mind, file等）
    document_type = Column(String(50), nullable=True)               # 文档类型（兼容字段）
    workspace_name = Column(String(200), nullable=True)             # 工作空间名称
    storage_strategy = Column(String(50), default="link_only")      # 存储策略
    access_url = Column(String(1000), nullable=True)                # 访问链接
    
    # 系统关联信息
    system_doc_id = Column(UUID(as_uuid=True), nullable=True)       # 关联的系统文档ID
    sync_direction = Column(String(20), default="bidirectional")    # 同步方向（to_dingtalk, from_dingtalk, bidirectional）
    
    # 同步状态
    sync_status = Column(String(20), default="synced")              # 同步状态（synced, pending, failed）
    last_sync_at = Column(DateTime(timezone=True), nullable=True)   # 最后同步时间
    dingtalk_updated_at = Column(DateTime(timezone=True), nullable=True)  # 钉钉文档更新时间
    system_updated_at = Column(DateTime(timezone=True), nullable=True)    # 系统文档更新时间
    
    # 权限信息
    permission_config = Column(JSON, nullable=True)                 # 权限配置
    is_public = Column(Boolean, default=False)                      # 是否公开
    
    # 元数据
    tags = Column(ARRAY(String), nullable=True)                     # 标签
    doc_metadata = Column(JSON, nullable=True)                      # 其他元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_documents")
    project = relationship("Project", backref="dingtalk_documents")
    user = relationship("User", backref="dingtalk_documents")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'dingtalk_doc_id', name='uq_dingtalk_document'),
        Index('idx_dingtalk_doc_space', 'dingtalk_space_id'),
        Index('idx_dingtalk_doc_sync_status', 'sync_status', 'last_sync_at'),
        Index('idx_dingtalk_doc_user', 'user_id', 'sync_status'),
    )

    def __repr__(self):
        return f"<DingTalkDocument(id={self.id}, title='{self.title}', doc_id='{self.dingtalk_doc_id}')>"


class DingTalkGroup(Base):
    """钉钉群组"""
    __tablename__ = "dingtalk_groups"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    group_id = Column(String(100), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    owner_id = Column(String(100), nullable=True)
    member_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_groups")
    project = relationship("Project", backref="dingtalk_groups")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'group_id', name='uq_dingtalk_group'),
    )

    def __repr__(self):
        return f"<DingTalkGroup(id={self.id}, name='{self.name}', group_id='{self.group_id}')>"


class DingTalkSettings(Base):
    """钉钉插件设置"""
    __tablename__ = "dingtalk_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 钉钉应用配置
    app_key = Column(String(128), nullable=True, comment="钉钉应用Key")
    app_secret = Column(String(256), nullable=True, comment="钉钉应用Secret")
    corp_id = Column(String(128), nullable=True, comment="企业ID")
    agent_id = Column(String(100), nullable=True, comment="应用 AgentID，用于发送工作通知")
    
    # Webhook配置
    webhook_url = Column(String(512), nullable=True, comment="机器人Webhook URL")
    callback_url = Column(String(512), nullable=True, comment="事件回调URL")
    aes_key = Column(String(256), nullable=True, comment="AES加密密钥")
    token = Column(String(256), nullable=True, comment="签名Token")
    
    # 基础设置
    enabled = Column(Boolean, default=True, comment="是否启用")
    enable_dingtalk = Column(Boolean, default=True, comment="是否启用钉钉功能")
    notification_level = Column(String(50), default="all", comment="通知级别")
    retry_count = Column(Integer, default=3, comment="重试次数")
    retry_interval = Column(Integer, default=60, comment="重试间隔(秒)")
    default_template = Column(Text, nullable=True, comment="默认消息模板")
    
    # 扩展配置
    config = Column(JSON, default=dict, comment="扩展配置")
    ai_integration_config = Column(JSON, default=dict, comment="AI集成配置")
    
    # AI助理直通模式配置
    ai_passthrough_enabled = Column(Boolean, default=False, comment="是否启用AI助理直通模式")
    ai_passthrough_token = Column(String(256), nullable=True, comment="AI助理直通模式安全Token")
    passthrough_ai_assistant_id = Column(UUID(as_uuid=True), nullable=True, comment="直通模式使用的AI助手ID")
    
    # 审计字段
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_settings")
    project = relationship("Project", backref="dingtalk_settings")

    __table_args__ = (
        UniqueConstraint('project_id', name='uq_dingtalk_settings_project'),
    )

    def __repr__(self):
        return f"<DingTalkSettings(id={self.id}, project_id='{self.project_id}')>"


class DingTalkNotificationLog(Base):
    """钉钉通知日志"""
    __tablename__ = "dingtalk_notification_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    webhook_id = Column(UUID(as_uuid=True), ForeignKey("dingtalk_webhooks.id", ondelete="SET NULL"), nullable=True)
    notification_type = Column(String(50), nullable=False)
    title = Column(String(200), nullable=True)
    content = Column(Text, nullable=False)
    status = Column(String(20), nullable=False)  # success, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    target_users = Column(ARRAY(String), default=list)
    target_groups = Column(ARRAY(String), default=list)
    response_data = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_notification_logs")
    project = relationship("Project", backref="dingtalk_notification_logs")
    webhook = relationship("DingTalkWebhook", backref="notification_logs")

    def __repr__(self):
        return f"<DingTalkNotificationLog(id={self.id}, status='{self.status}')>"


class DingTalkKnowledgeBase(Base):
    """钉钉知识库同步记录"""
    __tablename__ = "dingtalk_knowledge_bases"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    sync_type = Column(String(50), nullable=False)  # full, incremental, selective
    sync_status = Column(String(20), nullable=False)  # pending, running, completed, failed
    sync_result = Column(JSON, nullable=True)  # 同步结果详情
    synced_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_knowledge_bases")
    project = relationship("Project", backref="dingtalk_knowledge_bases")
    syncer = relationship("User", foreign_keys=[synced_by], backref="synced_dingtalk_knowledge_bases")

    def __repr__(self):
        return f"<DingTalkKnowledgeBase(id={self.id}, sync_type='{self.sync_type}', status='{self.sync_status}')>"


class DingTalkWebhook(Base):
    """钉钉Webhook配置（支持机器人通知和事件订阅）"""
    __tablename__ = "dingtalk_webhooks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="Webhook名称")
    description = Column(Text, nullable=True, comment="Webhook描述")
    webhook_type = Column(String(32), default="robot", comment="Webhook类型: robot(机器人), event(事件订阅)")
    
    # Webhook配置
    webhook_url = Column(String(512), nullable=True, comment="机器人Webhook URL")
    callback_url = Column(String(512), nullable=True, comment="事件回调URL")
    secret = Column(String(256), nullable=True, comment="机器人签名密钥（仅机器人通知使用）")
    enabled = Column(Boolean, default=True, comment="是否启用")
    
    # HTTP推送模式配置（钉钉官方推荐配置）
    subscription_mode = Column(String(32), default="http", comment="订阅模式: http(HTTP推送), stream(Stream模式)")
    aes_key = Column(String(256), nullable=True, comment="AES加密密钥（HTTP推送模式必需）")
    token = Column(String(256), nullable=True, comment="签名Token（HTTP推送模式必需，用于签名验证）")
    
    # Stream模式配置
    stream_endpoint = Column(String(512), nullable=True, comment="Stream模式连接端点")
    stream_status = Column(String(32), default="disconnected", comment="Stream连接状态: connected, disconnected, error")
    
    # 机器人通知配置
    message_template = Column(Text, nullable=True, comment="消息模板")
    notification_types = Column(ARRAY(String), default=list, comment="通知类型列表")
    target_users = Column(ARRAY(String), default=list, comment="目标用户ID列表")
    target_groups = Column(ARRAY(String), default=list, comment="目标群组ID列表")
    
    # 事件订阅配置
    event_types = Column(JSON, nullable=True, comment="订阅的事件类型列表")
    
    # 状态信息
    last_triggered_at = Column(DateTime(timezone=True), nullable=True, comment="最后触发时间")
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_webhooks")
    project = relationship("Project", backref="dingtalk_webhooks")
    creator = relationship("User", foreign_keys=[created_by], backref="created_dingtalk_webhooks")

    def __repr__(self):
        return f"<DingTalkWebhook(id={self.id}, name='{self.name}', type='{self.webhook_type}', enabled={self.enabled})>"


class DingTalkEventLog(Base):
    """钉钉事件处理日志"""
    __tablename__ = "dingtalk_event_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    webhook_id = Column(UUID(as_uuid=True), ForeignKey("dingtalk_webhooks.id", ondelete="SET NULL"), nullable=True)
    event_type = Column(String(64), nullable=False, comment="事件类型")
    event_id = Column(String(128), nullable=True, comment="事件ID")
    event_data = Column(JSON, nullable=False, comment="事件数据")
    signature = Column(String(256), nullable=True, comment="签名")
    timestamp = Column(String(32), nullable=True, comment="时间戳")
    status = Column(String(32), default="processed", comment="处理状态: pending, processed, failed")
    error_message = Column(Text, nullable=True, comment="错误信息")
    process_result = Column(JSON, nullable=True, comment="处理结果")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_event_logs")
    project = relationship("Project", backref="dingtalk_event_logs")
    webhook = relationship("DingTalkWebhook", backref="event_logs")

    def __repr__(self):
        return f"<DingTalkEventLog(id={self.id}, event_type='{self.event_type}', status='{self.status}')>"


class DingTalkAIIntegration(Base):
    """钉钉AI集成配置"""
    __tablename__ = "dingtalk_ai_integrations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    integration_type = Column(String(50), default="basic")  # basic, advanced, custom
    ai_model_config = Column(JSON, default=dict)  # AI模型配置
    auto_reply_enabled = Column(Boolean, default=False)  # 是否启用自动回复
    reply_templates = Column(JSON, default=dict)  # 回复模板
    trigger_keywords = Column(ARRAY(String), default=list)  # 触发关键词
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_ai_integrations")
    project = relationship("Project", backref="dingtalk_ai_integrations")
    creator = relationship("User", foreign_keys=[created_by], backref="created_dingtalk_ai_integrations")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', name='uq_dingtalk_ai_integration'),
    )

    def __repr__(self):
        return f"<DingTalkAIIntegration(id={self.id}, integration_type='{self.integration_type}')>"


class DingTalkApprovalInstance(Base):
    """钉钉审批实例"""
    __tablename__ = "dingtalk_approval_instances"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    instance_id = Column(String(128), unique=True, nullable=False, comment="钉钉审批实例ID")
    process_code = Column(String(128), nullable=False, comment="审批模板编码")
    title = Column(String(255), nullable=False, comment="审批标题")
    description = Column(Text, comment="审批描述")
    
    # 申请人信息
    originator_user_id = Column(String(128), nullable=False, comment="申请人钉钉用户ID")
    originator_name = Column(String(255), comment="申请人姓名")
    
    # 审批状态
    status = Column(String(32), nullable=False, default="NEW", comment="审批状态: NEW, RUNNING, COMPLETED, TERMINATED, CANCELED")
    biz_action = Column(String(32), comment="业务动作")
    
    # 时间信息
    create_time = Column(DateTime(timezone=True), comment="创建时间")
    finish_time = Column(DateTime(timezone=True), comment="完成时间")
    last_sync_time = Column(DateTime(timezone=True), comment="最后同步时间")
    
    # 表单数据
    form_component_values = Column(JSON, comment="表单组件值")
    amount = Column(String(50), comment="涉及金额")
    urgency_level = Column(String(32), comment="紧急程度")
    
    # 审批流程
    approval_process = Column(JSON, comment="审批流程数据")
    current_approver = Column(String(255), comment="当前审批人")
    
    # 抄送和附件
    cc_list = Column(JSON, comment="抄送人列表")
    attachments = Column(JSON, comment="附件列表")
    
    # 审批链接
    pc_url = Column(String(512), comment="PC端审批链接")
    mobile_url = Column(String(512), comment="移动端审批链接")
    
    # 创建时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_approval_instances")
    project = relationship("Project", backref="dingtalk_approval_instances")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'instance_id', name='uq_dingtalk_approval_instance'),
    )

    def __repr__(self):
        return f"<DingTalkApprovalInstance(id={self.id}, title='{self.title}', status='{self.status}')>"


class DingTalkApprovalTemplate(Base):
    """钉钉审批模板"""
    __tablename__ = "dingtalk_approval_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    process_code = Column(String(128), unique=True, nullable=False, comment="模板编码")
    name = Column(String(255), nullable=False, comment="模板名称")
    description = Column(Text, comment="模板描述")
    
    # 模板信息
    icon_url = Column(String(512), comment="图标URL")
    status = Column(String(32), default="ENABLE", comment="模板状态: ENABLE, DISABLE")
    template_type = Column(String(64), comment="模板类型")
    
    # 表单配置
    form_schema = Column(JSON, comment="表单结构定义")
    form_components = Column(JSON, comment="表单组件配置")
    
    # 流程配置
    approval_flow = Column(JSON, comment="审批流程配置")
    cc_settings = Column(JSON, comment="抄送配置")
    
    # 权限配置
    visible_users = Column(JSON, comment="可见用户列表")
    usable_users = Column(JSON, comment="可使用用户列表")
    
    # 时间信息
    gmt_create = Column(DateTime(timezone=True), comment="钉钉创建时间")
    gmt_modified = Column(DateTime(timezone=True), comment="钉钉修改时间")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_approval_templates")
    project = relationship("Project", backref="dingtalk_approval_templates")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'process_code', name='uq_dingtalk_approval_template'),
    )

    def __repr__(self):
        return f"<DingTalkApprovalTemplate(id={self.id}, name='{self.name}', process_code='{self.process_code}')>"


class DingTalkApprovalOperation(Base):
    """钉钉审批操作记录"""
    __tablename__ = "dingtalk_approval_operations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    instance_id = Column(String(128), ForeignKey("dingtalk_approval_instances.instance_id"), nullable=False)
    
    # 操作信息
    operation_type = Column(String(32), nullable=False, comment="操作类型: AGREE, REFUSE, REVOKE, REDIRECT")
    operation_result = Column(String(32), comment="操作结果")
    operation_time = Column(DateTime(timezone=True), comment="操作时间")
    
    # 操作人信息
    operator_user_id = Column(String(128), comment="操作人钉钉用户ID")
    operator_name = Column(String(255), comment="操作人姓名")
    
    # 操作详情
    remark = Column(Text, comment="操作备注")
    attachments = Column(JSON, comment="操作附件")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_approval_operations")
    project = relationship("Project", backref="dingtalk_approval_operations")

    def __repr__(self):
        return f"<DingTalkApprovalOperation(id={self.id}, operation_type='{self.operation_type}')>"


class DingTalkApprovalTask(Base):
    """钉钉审批任务"""
    __tablename__ = "dingtalk_approval_tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    process_instance_id = Column(String(128), nullable=False, comment="审批实例ID")
    task_id = Column(String(128), unique=True, nullable=False, comment="任务ID")
    
    # 任务信息
    user_id = Column(String(128), nullable=False, comment="任务处理人钉钉用户ID")
    status = Column(String(32), nullable=False, default="RUNNING", comment="任务状态: RUNNING, COMPLETED, CANCELED")
    result = Column(String(32), comment="任务结果: agree, refuse")
    
    # 任务数据
    task_data = Column(JSON, comment="任务详细数据")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_approval_tasks")
    project = relationship("Project", backref="dingtalk_approval_tasks")

    __table_args__ = (
        Index('idx_dingtalk_approval_task_instance', 'process_instance_id'),
        Index('idx_dingtalk_approval_task_user', 'user_id', 'status'),
    )

    def __repr__(self):
        return f"<DingTalkApprovalTask(id={self.id}, task_id='{self.task_id}', status='{self.status}')>"


class DingTalkApprovalStatistics(Base):
    """钉钉审批统计"""
    __tablename__ = "dingtalk_approval_statistics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 统计周期
    period_type = Column(String(32), nullable=False, comment="统计周期类型: day, week, month, quarter, year")
    period_start = Column(DateTime(timezone=True), nullable=False, comment="统计周期开始时间")
    period_end = Column(DateTime(timezone=True), nullable=False, comment="统计周期结束时间")
    
    # 统计数据
    total_instances = Column(Integer, default=0, comment="总审批数")
    pending_instances = Column(Integer, default=0, comment="待审批数")
    approved_instances = Column(Integer, default=0, comment="已通过数")
    rejected_instances = Column(Integer, default=0, comment="已拒绝数")
    canceled_instances = Column(Integer, default=0, comment="已撤销数")
    
    # 按类型统计
    type_statistics = Column(JSON, comment="按审批类型统计")
    
    # 效率统计
    avg_approval_time = Column(String(50), comment="平均审批时长")
    approval_rate = Column(String(50), comment="审批通过率")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_approval_statistics")
    project = relationship("Project", backref="dingtalk_approval_statistics")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'period_type', 'period_start', name='uq_dingtalk_approval_statistics'),
    )

    def __repr__(self):
        return f"<DingTalkApprovalStatistics(id={self.id}, period_type='{self.period_type}')>"


# DingTalkConfig 模型已删除，统一使用 DingTalkSettings


class DingTalkAccessToken(Base):
    """钉钉访问令牌"""
    __tablename__ = "dingtalk_access_tokens"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 令牌信息
    access_token = Column(String(512), nullable=False, comment="访问令牌")
    expires_at = Column(DateTime(timezone=True), nullable=False, comment="过期时间")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    project = relationship("Project", backref="dingtalk_access_tokens")

    __table_args__ = (
        UniqueConstraint('project_id', name='uq_dingtalk_access_token'),
    )

    def __repr__(self):
        return f"<DingTalkAccessToken(id={self.id}, project_id='{self.project_id}')>"
