#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
import json
import base64
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Body, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from sqlalchemy import select, and_

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User, ThirdPartyAccount
from models.project import Project
from models.ai import AIAssistant, AIAssistantThread, AIAssistantMessage
from models.knowledge_base import KnowledgeCategory, KnowledgeDocument

from ..models.models import DingTalkUserMapping, DingTalkSettings
from ..utils.dingtalk_api import DingTalkAPI
from ..utils.permission_manager import DingTalkPermissionManager, DingTalkPermission
from ..utils.ai_tools import DingTalkAITools
from services.ai import AIAssistantService, AIChatService
from services.knowledge_base import KnowledgeBaseService

# 初始化日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["钉钉AI聊天"]
)

@router.post("/ai/chat")
async def dingtalk_ai_chat(
    message: str = Form(..., description="用户消息"),
    assistant_id: Optional[str] = Form(None, description="AI助手ID"),
    thread_id: Optional[str] = Form(None, description="对话线程ID"),
    files: Optional[List[UploadFile]] = File(None, description="上传的文件"),
    use_knowledge_base: bool = Form(True, description="是否使用知识库"),
    knowledge_base_ids: Optional[List[str]] = Form(None, description="指定的知识库ID列表"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """钉钉AI聊天接口 - 支持文件、图片和知识库集成"""
    try:
        # 权限检查
        DingTalkPermissionManager.check_permission(
            current_user, project, DingTalkPermission.USE_AI_CHAT
        )
        
        # 检查用户是否绑定钉钉账号
        third_party_query = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.user_id == current_user.id,
                ThirdPartyAccount.platform == "dingtalk"
            )
        )
        result = await db.execute(third_party_query)
        third_party_account = result.scalar_one_or_none()
        
        if not third_party_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户未绑定钉钉账号"
            )
        
        # 处理上传的文件
        file_contents = []
        if files:
            for file in files:
                try:
                    # 检查文件类型
                    if file.content_type.startswith('image/'):
                        # 处理图片文件
                        content = await file.read()
                        base64_content = base64.b64encode(content).decode('utf-8')
                        file_contents.append({
                            "type": "image",
                            "filename": file.filename,
                            "content_type": file.content_type,
                            "content": base64_content
                        })
                    elif file.content_type in ['text/plain', 'application/pdf', 'application/msword']:
                        # 处理文档文件
                        content = await file.read()
                        if file.content_type == 'text/plain':
                            text_content = content.decode('utf-8')
                        else:
                            # 对于PDF和Word文档，这里简化处理
                            text_content = f"[文档文件: {file.filename}]"
                        
                        file_contents.append({
                            "type": "document",
                            "filename": file.filename,
                            "content_type": file.content_type,
                            "content": text_content
                        })
                    else:
                        logger.warning(f"不支持的文件类型: {file.content_type}")
                except Exception as e:
                    logger.error(f"处理文件 {file.filename} 失败: {str(e)}")
                    continue
        
        # 获取或创建AI助手
        if assistant_id:
            assistant = await AIAssistantService.get_assistant(db, uuid.UUID(assistant_id))
            if not assistant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="AI助手不存在"
                )
        else:
            # 使用默认的钉钉AI助手
            assistant_query = select(AIAssistant).where(
                and_(
                    AIAssistant.tenant_id == project.tenant_id,
                    AIAssistant.project_id == project.id,
                    AIAssistant.name == "钉钉智能助手"
                )
            )
            result = await db.execute(assistant_query)
            assistant = result.scalar_one_or_none()
            
            if not assistant:
                # 创建增强的钉钉AI助手
                from schemas.ai import AIAssistantCreate
                
                # 获取钉钉配置并初始化AI工具
                from services.system_config import SystemConfigService
                configs = await SystemConfigService.get_configs_by_type(db, project.tenant_id, "third_party_login")
                config_dict = {config.config_key: config.config_value for config in configs}
                dingtalk_config = config_dict.get("dingtalk", {})
                
                app_key = dingtalk_config.get("app_key")
                app_secret = dingtalk_config.get("app_secret")
                
                # 初始化AI工具集
                tools = []
                if app_key and app_secret:
                    dingtalk_api = DingTalkAPI(app_key, app_secret)
                    ai_tools = DingTalkAITools(dingtalk_api, db, project, current_user)
                    tools = await ai_tools.get_available_tools()
                else:
                    # 基础工具（不需要钉钉配置）
                    tools = [
                        {
                            "type": "knowledge_base_search",
                            "description": "搜索项目知识库获取相关信息"
                        }
                    ]
                
                assistant_data = AIAssistantCreate(
                    tenant_id=project.tenant_id,
                    project_id=project.id,
                    name="钉钉智能助手",
                    description="专为钉钉集成设计的AI助手，支持文件处理、知识库查询和钉钉操作",
                    instructions="""你是一个专业的钉钉智能工作助手，具备以下能力：

1. **文件处理能力**：
   - 可以分析用户上传的图片内容
   - 可以阅读和理解文档内容（PDF、Word、文本文件）
   - 基于文件内容回答问题和提供建议

2. **知识库集成**：
   - 可以搜索项目知识库获取相关信息
   - 结合知识库内容提供准确的答案
   - 引用知识库来源，确保信息可靠性

3. **钉钉操作能力**：
   - 可以发送消息到钉钉用户或群组
   - 可以查询用户和群组信息
   - 可以协助处理钉钉相关的工作流程

4. **智能分析能力**：
   - 分析业务数据和趋势
   - 提供决策建议和优化方案
   - 协助解决工作中的问题

请保持专业、友好的语调，提供准确有用的信息。当用户上传文件时，请仔细分析文件内容并给出相关建议。""",
                    is_public=False,
                    status="active",
                    tools=tools
                )
                assistant = await AIAssistantService.create_assistant(
                    db, assistant_data, current_user.id
                )
        
        # 获取或创建对话线程
        if thread_id:
            thread = await AIAssistantService.get_thread(db, uuid.UUID(thread_id))
            if not thread or thread.assistant_id != assistant.id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话线程不存在或不属于该助手"
                )
        else:
            # 创建新的对话线程
            from schemas.ai import AIAssistantThreadCreate
            thread_data = AIAssistantThreadCreate(
                assistant_id=assistant.id,
                user_id=current_user.id,
                title=f"钉钉聊天 - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                metadata={
                    "source": "dingtalk",
                    "dingtalk_user_id": third_party_account.platform_user_id,
                    "supports_files": True,
                    "supports_knowledge_base": use_knowledge_base
                }
            )
            thread = await AIAssistantService.create_thread(db, thread_data)
        
        # 准备聊天上下文
        chat_context = {
            "message": message,
            "files": file_contents,
            "use_knowledge_base": use_knowledge_base,
            "knowledge_base_ids": knowledge_base_ids,
            "dingtalk_context": {
                "user_id": third_party_account.platform_user_id,
                "user_name": third_party_account.platform_username
            }
        }
        
        # 如果启用知识库，搜索相关内容
        knowledge_context = ""
        if use_knowledge_base:
            try:
                # 使用新的AI知识库服务进行搜索
                from services.ai.knowledge_service import AIKnowledgeService
                
                search_result = await AIKnowledgeService.enhanced_search_with_reranking(
                    db=db,
                    query=message,
                    project_id=project.id,
                    top_k=3,
                    similarity_threshold=0.6,
                    user_id=current_user.id,
                    enable_semantic_reranking=True,
                    query_expansion=True
                )
                
                if search_result.get('success') and search_result.get('results'):
                    knowledge_results = search_result['results']
                    
                    # 构建智能摘要的知识库上下文
                    knowledge_parts = []
                    for i, result in enumerate(knowledge_results[:2], 1):  # 只使用前2个最相关的结果
                        content = result.get('content', '').strip()
                        document_title = result.get('document_title', '知识库文档')
                        similarity = result.get('similarity', 0)
                        
                        if content:
                            # 智能提取相关内容片段
                            relevant_content = await extract_relevant_snippet(
                                content, message, max_length=150
                            )
                            
                            if relevant_content:
                                knowledge_parts.append(
                                    f"📄 {document_title}（相关性：{similarity:.0%}）：{relevant_content}"
                                )
                    
                    if knowledge_parts:
                        knowledge_context = "\n\n🔍 相关知识库信息：\n" + "\n\n".join(knowledge_parts)
                        knowledge_context += f"\n\n💡 请基于上述信息回答关于「{message}」的问题。"
                        
                        # 将知识库内容添加到消息中
                        enhanced_message = f"{message}\n\n{knowledge_context}"
                        chat_context["message"] = enhanced_message
                        
            except Exception as e:
                logger.error(f"搜索知识库失败: {str(e)}")
                # 知识库搜索失败不影响正常聊天
        
        # 使用AI助手进行聊天
        from schemas.ai import AIAssistantChatRequest
        chat_request = AIAssistantChatRequest(
            assistant_id=assistant.id,
            thread_id=thread.id,
            content=chat_context["message"],
            content_type="text"
        )
        
        chat_response = await AIAssistantService.chat_with_assistant(
            db, chat_request, current_user.id
        )
        
        # 记录文件处理信息
        file_info = []
        if file_contents:
            for file_content in file_contents:
                file_info.append({
                    "filename": file_content["filename"],
                    "type": file_content["type"],
                    "processed": True
                })
        
        return {
            "success": True,
            "data": {
                "assistant_id": str(assistant.id),
                "thread_id": str(thread.id),
                "message": chat_response.get("message", ""),
                "usage": chat_response.get("usage", {}),
                "files_processed": file_info,
                "knowledge_base_used": use_knowledge_base and bool(knowledge_context),
                "created_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"钉钉AI聊天失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI聊天失败: {str(e)}"
        )

async def extract_relevant_snippet(content: str, query: str, max_length: int = 150) -> str:
    """
    从内容中提取与查询相关的片段
    """
    try:
        import re
        
        # 清理内容，移除Excel格式标记
        cleaned_content = content
        cleaned_content = re.sub(r'=== Sheet\d+ ===', '', cleaned_content)
        cleaned_content = re.sub(r'Unnamed: \d+', '', cleaned_content)
        
        # 移除多余的空行
        lines = [line.strip() for line in cleaned_content.split('\n') if line.strip()]
        cleaned_content = '\n'.join(lines)
        
        # 如果内容很短，直接返回
        if len(cleaned_content) <= max_length:
            return cleaned_content
        
        # 查找包含查询关键词的句子
        query_keywords = query.lower().split()
        sentences = re.split(r'[。！？\n]', cleaned_content)
        
        # 找到最相关的句子
        relevant_sentences = []
        for sentence in sentences:
            if not sentence.strip():
                continue
                
            sentence_lower = sentence.lower()
            score = sum(1 for keyword in query_keywords if keyword in sentence_lower)
            
            if score > 0:
                relevant_sentences.append((sentence.strip(), score))
        
        if relevant_sentences:
            # 按相关性排序
            relevant_sentences.sort(key=lambda x: x[1], reverse=True)
            
            # 拼接最相关的句子
            result = ""
            for sentence, _ in relevant_sentences:
                if len(result + sentence) <= max_length:
                    result += sentence + "。"
                else:
                    break
            
            if result:
                return result
        
        # 如果没有找到相关句子，返回开头部分
        return cleaned_content[:max_length] + "..." if len(cleaned_content) > max_length else cleaned_content
        
    except Exception as e:
        logger.error(f"提取相关片段失败: {e}")
        # 出错时返回截断的内容
        return content[:max_length] + "..." if len(content) > max_length else content

@router.post("/ai/send-to-dingtalk")
async def send_ai_response_to_dingtalk(
    message: str = Body(..., description="AI回复消息"),
    target_type: str = Body(..., description="发送目标类型: user/group"),
    target_id: str = Body(..., description="目标ID（用户ID或群组ID）"),
    message_type: str = Body("text", description="消息类型: text/card"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """将AI回复发送到钉钉 - 支持多种消息类型"""
    try:
        # 权限检查
        DingTalkPermissionManager.check_permission(
            current_user, project, DingTalkPermission.SEND_MESSAGE
        )
        
        # 检查用户是否绑定钉钉账号
        third_party_query = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.user_id == current_user.id,
                ThirdPartyAccount.platform == "dingtalk"
            )
        )
        result = await db.execute(third_party_query)
        third_party_account = result.scalar_one_or_none()
        
        if not third_party_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户未绑定钉钉账号"
            )
        
        # 获取钉钉配置
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整"
            )
        
        # 初始化钉钉API
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        
        # 根据消息类型发送不同格式的消息
        if message_type == "card":
            # 发送AI回复卡片
            result = await dingtalk_api.send_ai_response_card(
                target_type=target_type,
                target_id=target_id,
                ai_response=message,
                assistant_name="钉钉智能助手"
            )
        else:
            # 发送普通文本消息
            if target_type == "user":
                result = await dingtalk_api.send_work_notification(
                    user_id=target_id,
                    message=f"🤖 AI助手回复：\n\n{message}"
                )
            else:
                result = await dingtalk_api.send_group_message(
                    chat_id=target_id,
                    message=f"🤖 AI助手回复：\n\n{message}"
                )
        
        if result.get("success"):
            return {
                "success": True,
                "data": {
                    "message_id": result.get("message_id", str(uuid.uuid4())),
                    "sent_at": datetime.now().isoformat()
                }
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"发送消息失败: {result.get('error', '未知错误')}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送AI回复到钉钉失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送消息失败: {str(e)}"
        )

@router.post("/ai/sync-messages")
async def sync_dingtalk_messages(
    group_id: Optional[str] = Body(None, description="群组ID"),
    since_timestamp: Optional[int] = Body(None, description="同步起始时间戳"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """同步钉钉消息到系统"""
    try:
        # 权限检查
        DingTalkPermissionManager.check_permission(
            current_user, project, DingTalkPermission.SYNC_KNOWLEDGE
        )
        
        # 模拟同步成功
        return {
            "success": True,
            "data": {
                "synced_count": 0,
                "messages": [],
                "sync_timestamp": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步钉钉消息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步消息失败: {str(e)}"
        )

@router.get("/ai/assistants")
async def get_dingtalk_assistants(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取可用的AI助手列表"""
    try:
        assistants, total = await AIAssistantService.get_assistants(
            db=db,
            tenant_id=project.tenant_id,
            project_id=project.id,
            status="active"
        )
        
        return {
            "success": True,
            "data": {
                "assistants": [
                    {
                        "id": str(assistant.id),
                        "name": assistant.name,
                        "description": assistant.description,
                        "is_public": assistant.is_public,
                        "created_at": assistant.created_at.isoformat() if assistant.created_at else None
                    }
                    for assistant in assistants
                ],
                "total": total
            }
        }
        
    except Exception as e:
        logger.error(f"获取AI助手列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI助手列表失败: {str(e)}"
        )

@router.get("/ai/threads")
async def get_dingtalk_threads(
    assistant_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取用户的对话线程列表"""
    try:
        assistant_uuid = uuid.UUID(assistant_id) if assistant_id else None
        
        threads, total = await AIAssistantService.get_threads(
            db=db,
            assistant_id=assistant_uuid,
            user_id=current_user.id
        )
        
        return {
            "success": True,
            "data": {
                "threads": [
                    {
                        "id": str(thread.id),
                        "assistant_id": str(thread.assistant_id),
                        "title": thread.title,
                        "metadata": thread.metadata,
                        "created_at": thread.created_at.isoformat() if thread.created_at else None,
                        "updated_at": thread.updated_at.isoformat() if thread.updated_at else None
                    }
                    for thread in threads
                ],
                "total": total
            }
        }
        
    except Exception as e:
        logger.error(f"获取对话线程列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话线程列表失败: {str(e)}"
        )

@router.post("/ai/chat-json")
async def dingtalk_ai_chat_json(
    request_data: Dict[str, Any] = Body(..., description="聊天请求数据"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """钉钉AI聊天接口 - JSON格式"""
    try:
        # 从请求数据中提取参数
        message = request_data.get("message", "")
        assistant_id = request_data.get("assistant_id")
        thread_id = request_data.get("thread_id")
        use_knowledge_base = request_data.get("use_knowledge_base", True)
        knowledge_base_ids = request_data.get("knowledge_base_ids", [])
        
        if not message:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="消息内容不能为空"
            )
        
        # 权限检查
        DingTalkPermissionManager.check_permission(
            current_user, project, DingTalkPermission.USE_AI_CHAT
        )
        
        # 检查用户是否绑定钉钉账号
        third_party_query = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.user_id == current_user.id,
                ThirdPartyAccount.platform == "dingtalk"
            )
        )
        result = await db.execute(third_party_query)
        third_party_account = result.scalar_one_or_none()
        
        if not third_party_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户未绑定钉钉账号"
            )
        
        # 获取或创建AI助手
        if assistant_id:
            assistant = await AIAssistantService.get_assistant(db, uuid.UUID(assistant_id))
            if not assistant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="AI助手不存在"
                )
        else:
            # 使用默认的钉钉AI助手
            assistant_query = select(AIAssistant).where(
                and_(
                    AIAssistant.tenant_id == project.tenant_id,
                    AIAssistant.project_id == project.id,
                    AIAssistant.name == "钉钉智能助手"
                )
            )
            result = await db.execute(assistant_query)
            assistant = result.scalar_one_or_none()
            
            if not assistant:
                # 创建简化的钉钉AI助手
                from schemas.ai import AIAssistantCreate
                
                assistant_data = AIAssistantCreate(
                    tenant_id=project.tenant_id,
                    project_id=project.id,
                    name="钉钉智能助手",
                    description="专为钉钉集成设计的AI助手",
                    instructions="你是一个专业的钉钉智能工作助手，可以帮助用户处理各种工作相关的问题。",
                    is_public=False,
                    status="active",
                    tools=[]
                )
                assistant = await AIAssistantService.create_assistant(
                    db, assistant_data, current_user.id
                )
        
        # 获取或创建对话线程
        if thread_id:
            thread = await AIAssistantService.get_thread(db, uuid.UUID(thread_id))
            if not thread or thread.assistant_id != assistant.id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="对话线程不存在或不属于该助手"
                )
        else:
            # 创建新的对话线程
            from schemas.ai import AIAssistantThreadCreate
            thread_data = AIAssistantThreadCreate(
                assistant_id=assistant.id,
                user_id=current_user.id,
                title=f"钉钉聊天 - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                metadata={
                    "source": "dingtalk",
                    "dingtalk_user_id": third_party_account.platform_user_id,
                    "supports_knowledge_base": use_knowledge_base
                }
            )
            thread = await AIAssistantService.create_thread(db, thread_data)
        
        # 准备聊天上下文
        chat_context = {
            "message": message,
            "use_knowledge_base": use_knowledge_base,
            "knowledge_base_ids": knowledge_base_ids,
            "dingtalk_context": {
                "user_id": third_party_account.platform_user_id,
                "user_name": third_party_account.platform_username
            }
        }
        
        # 如果启用知识库，搜索相关内容
        knowledge_context = ""
        if use_knowledge_base:
            try:
                # 使用新的AI知识库服务进行搜索
                from services.ai.knowledge_service import AIKnowledgeService
                
                search_result = await AIKnowledgeService.enhanced_search_with_reranking(
                    db=db,
                    query=message,
                    project_id=project.id,
                    top_k=3,
                    similarity_threshold=0.6,
                    user_id=current_user.id,
                    enable_semantic_reranking=True,
                    query_expansion=True
                )
                
                if search_result.get('success') and search_result.get('results'):
                    knowledge_results = search_result['results']
                    
                    # 构建智能摘要的知识库上下文
                    knowledge_parts = []
                    for i, result in enumerate(knowledge_results[:2], 1):  # 只使用前2个最相关的结果
                        content = result.get('content', '').strip()
                        document_title = result.get('document_title', '知识库文档')
                        similarity = result.get('similarity', 0)
                        
                        if content:
                            # 智能提取相关内容片段
                            relevant_content = await extract_relevant_snippet(
                                content, message, max_length=150
                            )
                            
                            if relevant_content:
                                knowledge_parts.append(
                                    f"📄 {document_title}（相关性：{similarity:.0%}）：{relevant_content}"
                                )
                    
                    if knowledge_parts:
                        knowledge_context = "\n\n🔍 相关知识库信息：\n" + "\n\n".join(knowledge_parts)
                        knowledge_context += f"\n\n💡 请基于上述信息回答关于「{message}」的问题。"
                        
                        # 将知识库内容添加到消息中
                        enhanced_message = f"{message}\n\n{knowledge_context}"
                        chat_context["message"] = enhanced_message
                        
            except Exception as e:
                logger.error(f"搜索知识库失败: {str(e)}")
                # 知识库搜索失败不影响正常聊天
        
        # 使用AI助手进行聊天
        from schemas.ai import AIAssistantChatRequest
        chat_request = AIAssistantChatRequest(
            assistant_id=assistant.id,
            thread_id=thread.id,
            content=chat_context["message"],
            content_type="text"
        )
        
        chat_response = await AIAssistantService.chat_with_assistant(
            db, chat_request, current_user.id
        )
        
        return {
            "success": True,
            "data": {
                "assistant_id": str(assistant.id),
                "thread_id": str(thread.id),
                "message": chat_response.get("message", ""),
                "usage": chat_response.get("usage", {}),
                "knowledge_base_used": use_knowledge_base and bool(knowledge_context),
                "created_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"钉钉AI聊天失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI聊天失败: {str(e)}"
        ) 