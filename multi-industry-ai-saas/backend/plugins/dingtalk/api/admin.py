#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Body, status, Query, Path
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
import asyncio
import time

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant
from models.user import User, ThirdPartyAccount
from models.project import Project
from models.tenant import Tenant
from models.store import Store
from models.role import Role

from ..models.models import DingTalkSettings, DingTalkKnowledgeBase, DingTalkAIIntegration, DingTalkWebhook, DingTalkUserMapping, DingTalkGroup, DingTalkNotificationLog, DingTalkDepartment, DingTalkTodoTask, DingTalkDocument, DingTalkSpace
from ..utils.permission_manager import DingTalk<PERSON>er<PERSON>Manager, DingTalkPermission
from ..utils.token_manager import token_manager
from ..utils.knowledge_sync import DingTalkKnowledgeSync
from ..utils.organization_sync import DingTalkOrganizationSyncService
from ..utils.todo_sync import TodoTaskSyncService
from ..utils.document_sync import DocumentSyncService
from ..utils.dingtalk_api import DingTalkAPI
from ..services.union_id_service import UnionIdService
from ..services.unified_settings_service import UnifiedSettingsService
from services.ai.chat_service import AIChatService
from services.ai.assistant_service import AIAssistantService
from schemas.ai.chat import AIChatMessage
from schemas.ai.assistant import AIAssistantThreadCreate, AIAssistantChatRequest
from models.ai.assistant import AIAssistantThread

# Pydantic 模型
class DingTalkUserMappingResponse(BaseModel):
    id: uuid.UUID
    user_id: Optional[uuid.UUID]
    dingtalk_union_id: Optional[str]
    dingtalk_user_id: Optional[str]
    dingtalk_name: Optional[str]
    dingtalk_avatar: Optional[str]
    is_active: Optional[bool]
    updated_at: datetime

    class Config:
        orm_mode = True

class SyncUserMappingSuccessResponse(BaseModel):
    success: bool
    data: DingTalkUserMappingResponse

class DingTalkAIPassthroughRequest(BaseModel):
    """钉钉AI助理直通模式请求体"""
    query: str
    context: Optional[Dict[str, Any]] = None
    # 根据钉钉文档，可能还有其他字段，如userId, corpId, etc.
    # 这里我们只关注核心的 query

# 初始化日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["钉钉插件管理员功能"]
)

async def get_dingtalk_config(db: AsyncSession, tenant_id: str, project_id: str) -> Dict[str, str]:
    """统一获取钉钉配置的函数"""
    settings_service = UnifiedSettingsService(db, tenant_id, project_id)
    return await settings_service.get_config_for_api()

@router.get("/admin/capabilities")
async def get_admin_capabilities(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户的管理员能力"""
    try:
        capabilities = DingTalkPermissionManager.get_user_capabilities(current_user, current_project)
        
        return {
            "success": True,
            "data": capabilities
        }
        
    except Exception as e:
        logger.error(f"获取管理员能力失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取管理员能力失败: {str(e)}"
        )

@router.post("/admin/sync-knowledge")
async def sync_dingtalk_knowledge(
    sync_options: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """同步钉钉知识库（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_KNOWLEDGE
        )
        
        # 获取访问令牌
        access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取钉钉访问令牌，请确保已正确配置钉钉应用或用户已绑定钉钉账号"
            )
        
        # 初始化知识库同步器
        knowledge_sync = DingTalkKnowledgeSync(access_token, db)
        
        # 执行同步
        sync_result = await knowledge_sync.sync_knowledge_base(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            options=sync_options
        )
        
        # 保存同步记录
        knowledge_record = DingTalkKnowledgeBase(
            id=uuid.uuid4(),
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            sync_type=sync_options.get("sync_type", "full"),
            sync_status="completed" if sync_result.get("success") else "failed",
            sync_result=sync_result,
            synced_by=current_user.id,
            created_at=datetime.now()
        )
        
        db.add(knowledge_record)
        await db.commit()
        
        return {
            "success": True,
            "message": "知识库同步完成",
            "data": sync_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步钉钉知识库失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步知识库失败: {str(e)}"
        )

@router.get("/admin/statistics")
async def get_dingtalk_statistics(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取钉钉插件统计信息"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.VIEW_STATISTICS
        )
        
        # 获取各种统计数据
        # 用户数统计
        users_query = select(func.count(DingTalkUserMapping.id)).where(
            and_(
                DingTalkUserMapping.tenant_id == current_tenant.id,
                DingTalkUserMapping.project_id == current_project.id,
                DingTalkUserMapping.is_active == True
            )
        )
        users_result = await db.execute(users_query)
        users_count = users_result.scalar() or 0
        
        # 通知发送数统计
        notifications_query = select(func.count(DingTalkNotificationLog.id)).where(
            and_(
                DingTalkNotificationLog.tenant_id == current_tenant.id,
                DingTalkNotificationLog.project_id == current_project.id
            )
        )
        notifications_result = await db.execute(notifications_query)
        notifications_sent = notifications_result.scalar() or 0
        
        # 待办任务数统计
        todo_tasks_query = select(func.count(DingTalkTodoTask.id)).where(
            and_(
                DingTalkTodoTask.tenant_id == current_tenant.id,
                DingTalkTodoTask.project_id == current_project.id
            )
        )
        todo_result = await db.execute(todo_tasks_query)
        todo_tasks_count = todo_result.scalar() or 0
        
        # 文档同步数统计
        documents_query = select(func.count(DingTalkDocument.id)).where(
            and_(
                DingTalkDocument.tenant_id == current_tenant.id,
                DingTalkDocument.project_id == current_project.id
            )
        )
        documents_result = await db.execute(documents_query)
        documents_synced = documents_result.scalar() or 0
        
        # 部门数统计
        departments_query = select(func.count(DingTalkDepartment.id)).where(
            and_(
                DingTalkDepartment.tenant_id == current_tenant.id,
                DingTalkDepartment.project_id == current_project.id
            )
        )
        departments_result = await db.execute(departments_query)
        departments_count = departments_result.scalar() or 0
        
        return {
            "success": True,
            "data": {
                "users_count": users_count,
                "notifications_sent": notifications_sent,
                "ai_chats_count": 0,  # 暂时没有AI对话记录表
                "todo_tasks_count": todo_tasks_count,
                "approval_instances_count": 0,  # 暂时返回0，待审批功能完善后再统计
                "documents_synced": documents_synced,
                "departments_count": departments_count
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取钉钉统计信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

@router.get("/admin/knowledge-sync-history")
async def get_knowledge_sync_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取知识库同步历史（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_KNOWLEDGE
        )
        
        # 查询同步历史
        query = select(DingTalkKnowledgeBase).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        ).order_by(DingTalkKnowledgeBase.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        records = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(DingTalkKnowledgeBase.id)).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        )
        count_result = await db.execute(count_query)
        total_count = count_result.scalar() or 0
        
        # 格式化数据
        history_data = [
            {
                "id": str(record.id),
                "sync_type": record.sync_type,
                "sync_status": record.sync_status,
                "sync_result": record.sync_result,
                "synced_by": str(record.synced_by),
                "created_at": record.created_at.isoformat() if record.created_at else None
            }
            for record in records
        ]
        
        return {
            "success": True,
            "data": {
                "history": history_data,
                "pagination": {
                    "current": (skip // limit) + 1,
                    "pageSize": limit,
                    "total": total_count
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库同步历史失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同步历史失败: {str(e)}"
        )

@router.post("/admin/sync-organization")
async def sync_organization_structure(
    sync_options: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """增强组织架构同步（统一使用增强同步服务）"""
    logger.info(f"🚀 开始增强组织架构同步，项目: {current_project.name}")
    logger.info(f"📋 同步选项: {sync_options}")
    
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_ORGANIZATION
        )
        
        # 获取钉钉配置
        config = await get_dingtalk_config(db, str(current_tenant.id), str(current_project.id))
        if not config["has_config"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉应用未配置，请先配置 App Key 和 App Secret"
            )
        
        # 初始化钉钉API
        dingtalk_api = DingTalkAPI(config["app_key"], config["app_secret"])
        
        # 导入增强组织架构同步服务
        enhanced_sync_service = DingTalkOrganizationSyncService(dingtalk_api, db)
        
        logger.info("🔧 开始执行增强组织架构同步...")
        sync_start_time = time.time()
        
        # 执行增强同步
        result = await enhanced_sync_service.full_sync_with_user_creation(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            sync_options=sync_options
        )
        
        sync_duration = time.time() - sync_start_time
        logger.info(f"⏱️ 增强组织架构同步完成，耗时: {sync_duration:.2f}秒")
        
        if result["success"]:
            logger.info(f"✅ 增强组织架构同步成功: {result['message']}")
            logger.info(f"📊 同步统计: {result['data']}")
        else:
            logger.error(f"❌ 增强组织架构同步失败: {result['message']}")
            if result["data"].get("errors"):
                logger.error(f"🚫 错误详情: {result['data']['errors']}")
        
        return {
            "success": result["success"],
            "message": result["message"],
            "data": result["data"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 增强组织架构同步异常: {str(e)}", exc_info=True)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"增强组织架构同步失败: {str(e)}"
        )

@router.get("/admin/organization-status")
async def get_organization_sync_status(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取组织架构同步状态（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_ORGANIZATION
        )
        
        # 查询部门数量
        dept_count_query = select(func.count(DingTalkDepartment.id)).where(
            and_(
                DingTalkDepartment.tenant_id == current_tenant.id,
                DingTalkDepartment.project_id == current_project.id
            )
        )
        dept_count_result = await db.execute(dept_count_query)
        dept_count = dept_count_result.scalar() or 0
        
        # 查询用户映射数量
        user_mapping_count_query = select(func.count(DingTalkUserMapping.id)).where(
            and_(
                DingTalkUserMapping.tenant_id == current_tenant.id,
                DingTalkUserMapping.project_id == current_project.id,
                DingTalkUserMapping.is_active == True
            )
        )
        user_mapping_count_result = await db.execute(user_mapping_count_query)
        user_mapping_count = user_mapping_count_result.scalar() or 0
        
        # 查询所有部门（用于构建完整的组织架构树）
        all_dept_query = select(DingTalkDepartment).where(
            and_(
                DingTalkDepartment.tenant_id == current_tenant.id,
                DingTalkDepartment.project_id == current_project.id
            )
        ).order_by(DingTalkDepartment.level.asc(), DingTalkDepartment.order.asc())
        
        all_dept_result = await db.execute(all_dept_query)
        all_departments = all_dept_result.scalars().all()
        
        # 查询最近同步的部门（用于显示最近活动）
        recent_dept_query = select(DingTalkDepartment).where(
            and_(
                DingTalkDepartment.tenant_id == current_tenant.id,
                DingTalkDepartment.project_id == current_project.id
            )
        ).order_by(DingTalkDepartment.updated_at.desc()).limit(10)
        
        recent_dept_result = await db.execute(recent_dept_query)
        recent_departments = recent_dept_result.scalars().all()
        
        # 查询最近同步的用户映射
        recent_user_query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.tenant_id == current_tenant.id,
                DingTalkUserMapping.project_id == current_project.id,
                DingTalkUserMapping.is_active == True
            )
        ).order_by(DingTalkUserMapping.updated_at.desc()).limit(10)
        
        recent_user_result = await db.execute(recent_user_query)
        recent_user_mappings = recent_user_result.scalars().all()
        
        # 查询所有用户映射（用于前端显示）
        all_user_query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.tenant_id == current_tenant.id,
                DingTalkUserMapping.project_id == current_project.id,
                DingTalkUserMapping.is_active == True
            )
        ).order_by(DingTalkUserMapping.updated_at.desc())
        
        all_user_result = await db.execute(all_user_query)
        all_user_mappings = all_user_result.scalars().all()

        return {
            "success": True,
            "data": {
                "statistics": {
                    "department_count": dept_count,
                    "user_mapping_count": user_mapping_count,
                    "last_sync_time": max(
                        [dept.updated_at for dept in recent_departments] + 
                        [user.updated_at for user in recent_user_mappings],
                        default=None
                    ).isoformat() if recent_departments or recent_user_mappings else None
                },
                "departments": [
                    {
                        "id": str(dept.id),
                        "dept_id": dept.dept_id,
                        "dingtalk_dept_id": dept.dept_id,
                        "name": dept.name,
                        "parent_id": dept.parent_id,
                        "dept_path": dept.dept_path,
                        "level": dept.level,
                        "member_count": dept.member_count,
                        "updated_at": dept.updated_at.isoformat() if dept.updated_at else None
                    }
                    for dept in all_departments
                ],
                "users": [
                    {
                        "id": str(mapping.id),
                        "user_id": str(mapping.user_id) if mapping.user_id else None,  # 修复：当user_id为None时返回null而不是"None"
                        "dingtalk_user_id": mapping.dingtalk_user_id,
                        "dingtalk_name": mapping.dingtalk_name,
                        "dingtalk_mobile": mapping.dingtalk_mobile,
                        "dingtalk_email": mapping.dingtalk_email,
                        "dingtalk_dept_id": mapping.dingtalk_dept_id,
                        "dingtalk_dept_name": mapping.dingtalk_dept_name,
                        "dingtalk_title": mapping.dingtalk_title,
                        "dingtalk_position": mapping.dingtalk_position,  # 添加职位字段
                        "department_id": mapping.dingtalk_dept_id,  # 用于前端部门关联
                        "updated_at": mapping.updated_at.isoformat() if mapping.updated_at else None
                    }
                    for mapping in all_user_mappings
                ],
                "recent_departments": [
                    {
                        "id": str(dept.id),
                        "dept_id": dept.dept_id,
                        "name": dept.name,
                        "dept_path": dept.dept_path,
                        "level": dept.level,
                        "member_count": dept.member_count,
                        "updated_at": dept.updated_at.isoformat() if dept.updated_at else None
                    }
                    for dept in recent_departments
                ],
                "recent_user_mappings": [
                    {
                        "id": str(mapping.id),
                        "dingtalk_name": mapping.dingtalk_name,
                        "dingtalk_dept_name": mapping.dingtalk_dept_name,
                        "dingtalk_title": mapping.dingtalk_title,
                        "updated_at": mapping.updated_at.isoformat() if mapping.updated_at else None
                    }
                    for mapping in recent_user_mappings
                ]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取组织架构同步状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同步状态失败: {str(e)}"
        )

# ==================== 钉钉文档集成 API ====================

@router.post("/admin/sync-documents")
async def sync_dingtalk_documents(
    sync_options: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """同步钉钉文档和知识库（管理员功能）"""
    try:
        logger.info(f"🚀 收到文档同步请求")
        logger.info(f"📝 请求数据: {sync_options}")
        logger.info(f"👤 当前用户: {current_user.username}")
        logger.info(f"🏢 当前项目: {current_project.name}")
        
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_DOCUMENTS
        )
        
        # 获取钉钉配置
        config = await get_dingtalk_config(db, str(current_tenant.id), str(current_project.id))
        app_key = config.get("app_key")
        app_secret = config.get("app_secret")
        has_config = config.get("has_config", False)
        
        logger.info(f"⚙️ 配置检查: has_config={has_config}, app_key存在={bool(app_key)}")
        
        if not has_config:
            return {
                "success": False,
                "message": "钉钉配置不完整，无法同步文档"
            }
        
        # 初始化钉钉API和文档同步服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        
        # 检查同步选项
        sync_type = sync_options.get("sync_type", "full")
        space_id = sync_options.get("space_id")
        auto_sync = sync_options.get("auto_sync", True)
        
        logger.info(f"📋 同步配置:")
        logger.info(f"   - 同步类型: {sync_type}")
        logger.info(f"   - 指定知识库ID: {space_id}")
        logger.info(f"   - 自动同步: {auto_sync}")
        
        # 初始化文档同步器
        document_sync = DocumentSyncService(dingtalk_api, db)
        
        # 执行文档同步
        if space_id:
            logger.info(f"🔄 开始同步指定知识库: {space_id}")
            # 同步指定知识库
            result = await document_sync.sync_specific_space(
                tenant_id=current_tenant.id,
                project_id=current_project.id,
                space_id=space_id,
                sync_options=sync_options
            )
        else:
            logger.info(f"🔄 开始同步所有工作空间和知识库")
            # 同步所有工作空间和知识库
            result = await document_sync.sync_workspaces_and_spaces(
                tenant_id=current_tenant.id,
                project_id=current_project.id,
                user_id=current_user.id,
                sync_options=sync_options
            )
        
        logger.info(f"✅ 文档同步完成，结果: {result}")
        
        return {
            "success": True,
            "message": "钉钉文档同步完成",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"❌ 同步钉钉文档失败: {e}", exc_info=True)
        await db.rollback()
        return {
            "success": False,
            "message": f"同步文档失败: {str(e)}"
        }

@router.get("/admin/documents")
async def get_project_documents(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    space_id: Optional[str] = Query(None),
    sync_status: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取项目的钉钉文档列表（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.VIEW_DOCUMENTS
        )
        
        # 构建过滤条件
        filters = {}
        if space_id:
            filters["space_id"] = space_id
        if sync_status:
            filters["sync_status"] = sync_status
        
        # 获取钉钉配置（使用统一设置服务）
        settings_service = UnifiedSettingsService(db, str(current_tenant.id), str(current_project.id))
        settings = await settings_service.get_settings()
        
        app_key = settings.get("app_key", "")
        app_secret = settings.get("app_secret", "")
        
        # 初始化文档同步服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        document_sync = DocumentSyncService(dingtalk_api, db)
        
        # 获取文档列表
        documents_result = await document_sync.get_project_documents(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            filters=filters
        )
        
        return {
            "success": True,
            "data": documents_result.get("data", {})
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档列表失败: {str(e)}"
        )

@router.get("/admin/spaces")
async def get_dingtalk_spaces(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取钉钉知识库列表（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.VIEW_DOCUMENTS
        )
        
        # 查询知识库列表
        query = select(DingTalkSpace).where(
            and_(
                DingTalkSpace.tenant_id == current_tenant.id,
                DingTalkSpace.project_id == current_project.id
            )
        ).order_by(DingTalkSpace.updated_at.desc())
        
        result = await db.execute(query)
        spaces = result.scalars().all()
        
        # 格式化数据
        spaces_data = [
            {
                "id": str(space.id),
                "dingtalk_space_id": space.dingtalk_space_id,
                "dingtalk_workspace_id": space.dingtalk_workspace_id,
                "space_name": space.space_name,
                "space_description": space.space_description,
                "doc_count": space.doc_count,
                "sync_status": space.sync_status,
                "auto_sync": space.auto_sync,
                "last_sync_at": space.last_sync_at.isoformat() if space.last_sync_at else None,
                "created_at": space.created_at.isoformat() if space.created_at else None,
                "updated_at": space.updated_at.isoformat() if space.updated_at else None
            }
            for space in spaces
        ]
        
        # 计算统计信息
        total_documents = sum(space.doc_count or 0 for space in spaces)
        synced_spaces = len([space for space in spaces if space.sync_status == 'synced'])
        
        return {
            "success": True,
            "data": {
                "total_count": len(spaces_data),
                "spaces": spaces_data,
                "statistics": {
                    "total_workspaces": len(spaces_data),
                    "total_documents": total_documents,
                    "synced_documents": total_documents,  # 假设已同步的知识库中的文档都是已同步的
                    "sync_rate": round((synced_spaces / len(spaces_data)) * 100) if spaces_data else 0
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取钉钉知识库列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识库列表失败: {str(e)}"
        )

@router.post("/admin/upload-to-dingtalk")
async def upload_knowledge_to_dingtalk(
    upload_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """将系统知识库内容上传到钉钉（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.UPLOAD_DOCUMENTS
        )
        
        knowledge_base_ids = upload_data.get("knowledge_base_ids", [])
        target_space_id = upload_data.get("target_space_id")
        sync_options = upload_data.get("sync_options", {})
        
        if not knowledge_base_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请选择要上传的知识库"
            )
        
        # 获取钉钉配置
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, current_project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，无法上传文档"
            )
        
        # 初始化钉钉API和文档同步服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        document_sync = DocumentSyncService(dingtalk_api, db)
        
        # 执行知识库上传
        upload_result = await document_sync.upload_knowledge_bases_to_dingtalk(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            knowledge_base_ids=knowledge_base_ids,
            target_space_id=target_space_id,
            sync_options=sync_options,
            user_id=current_user.id
        )
        
        return {
            "success": True,
            "message": f"成功上传 {upload_result.get('uploaded_count', 0)} 个文档到钉钉",
            "data": upload_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传知识库到钉钉失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传失败: {str(e)}"
        )

# ==================== 钉钉待办任务管理 API ====================

@router.post("/admin/todo-tasks/create")
async def create_todo_task(
    task_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """创建钉钉待办任务（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_USERS
        )
        
        # 获取钉钉配置
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, current_project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，无法创建待办任务"
            )
        
        # 初始化服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        todo_service = TodoTaskSyncService(dingtalk_api, db)
        
        # 创建待办任务
        result = await todo_service.create_todo_task_in_dingtalk(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            user_id=uuid.UUID(task_data["user_id"]),
            task_data=task_data
        )
        
        return {
            "success": result["success"],
            "message": result["message"],
            "data": result.get("data")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建钉钉待办任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建待办任务失败: {str(e)}"
        )

@router.post("/admin/todo-tasks/batch-create")
async def batch_create_todo_tasks(
    tasks_data: List[Dict[str, Any]] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """批量创建钉钉待办任务（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_USERS
        )
        
        # 获取钉钉配置
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, current_project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，无法创建待办任务"
            )
        
        # 初始化服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        todo_service = TodoTaskSyncService(dingtalk_api, db)
        
        # 批量创建待办任务
        result = await todo_service.batch_create_todo_tasks(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            tasks_data=tasks_data
        )
        
        return {
            "success": result["success"],
            "message": f"批量创建完成：成功 {result['success_count']} 个，失败 {result['failed_count']} 个",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量创建钉钉待办任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量创建待办任务失败: {str(e)}"
        )

@router.put("/admin/todo-tasks/{task_id}")
async def update_todo_task(
    task_id: str,
    task_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """更新钉钉待办任务（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_USERS
        )
        
        # 获取钉钉配置
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, current_project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，无法更新待办任务"
            )
        
        # 初始化服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        todo_service = TodoTaskSyncService(dingtalk_api, db)
        
        # 更新待办任务
        result = await todo_service.update_todo_task_in_dingtalk(
            task_id=uuid.UUID(task_id),
            task_data=task_data
        )
        
        return {
            "success": result["success"],
            "message": result["message"],
            "data": result.get("data")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新钉钉待办任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新待办任务失败: {str(e)}"
        )

@router.post("/admin/todo-tasks/{task_id}/complete")
async def complete_todo_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """完成钉钉待办任务（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_USERS
        )
        
        # 获取钉钉配置
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, current_project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，无法完成待办任务"
            )
        
        # 初始化服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        todo_service = TodoTaskSyncService(dingtalk_api, db)
        
        # 完成待办任务
        result = await todo_service.complete_todo_task(
            task_id=uuid.UUID(task_id),
            completed_by="admin"
        )
        
        return {
            "success": result["success"],
            "message": result["message"],
            "data": result.get("data")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"完成钉钉待办任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"完成待办任务失败: {str(e)}"
        )

@router.get("/admin/todo-tasks")
async def get_todo_tasks(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    user_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取钉钉待办任务列表（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.VIEW_USER_INFO
        )
        
        # 构建查询条件
        conditions = [
            DingTalkTodoTask.tenant_id == current_tenant.id,
            DingTalkTodoTask.project_id == current_project.id
        ]
        
        if status:
            conditions.append(DingTalkTodoTask.status == status)
        
        if user_id:
            conditions.append(DingTalkTodoTask.user_id == uuid.UUID(user_id))
        
        # 查询任务列表
        query = select(DingTalkTodoTask).where(
            and_(*conditions)
        ).order_by(DingTalkTodoTask.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(DingTalkTodoTask.id)).where(and_(*conditions))
        count_result = await db.execute(count_query)
        total_count = count_result.scalar() or 0
        
        # 格式化数据
        tasks_data = [
            {
                "id": str(task.id),
                "user_id": str(task.user_id),
                "system_task_id": str(task.system_task_id) if task.system_task_id else None,
                "dingtalk_record_id": task.dingtalk_record_id,
                "title": task.title,
                "description": task.description,
                "priority": task.priority,
                "status": task.status,
                "dingtalk_status": task.dingtalk_status,
                "sync_status": task.sync_status,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "last_sync_at": task.last_sync_at.isoformat() if task.last_sync_at else None,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None
            }
            for task in tasks
        ]
        
        return {
            "success": True,
            "data": {
                "tasks": tasks_data,
                "pagination": {
                    "current": (skip // limit) + 1,
                    "pageSize": limit,
                    "total": total_count
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取钉钉待办任务列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取待办任务列表失败: {str(e)}"
        )

@router.delete("/admin/todo-tasks/{task_id}")
async def delete_todo_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """删除钉钉待办任务（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_TODO_TASKS
        )
        
        # 查找任务
        query = select(DingTalkTodoTask).where(
            and_(
                DingTalkTodoTask.id == uuid.UUID(task_id),
                DingTalkTodoTask.tenant_id == current_tenant.id,
                DingTalkTodoTask.project_id == current_project.id
            )
        )
        result = await db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="待办任务不存在"
            )
        
        # 获取钉钉配置
        settings_service = UnifiedSettingsService(db, str(current_tenant.id), str(current_project.id))
        settings = await settings_service.get_settings()
        config = type('Config', (), settings)()
        
        if not config or not config.app_key or not config.app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，请先在插件设置中配置APP_KEY和APP_SECRET"
            )
        
        # 初始化钉钉API
        dingtalk_api = DingTalkAPI(config.app_key, config.app_secret)
        
        # 如果有钉钉任务ID，尝试从钉钉删除
        if task.dingtalk_task_id:
            try:
                # 使用UnionIdService获取任务创建者的unionId
                union_id_service = UnionIdService(db, dingtalk_api, str(current_tenant.id), str(current_project.id))
                task_creator_union_id = await union_id_service.get_union_id_by_user_id(str(task.user_id))
                
                # 获取当前操作者的unionId作为operatorId
                current_user_union_id = await union_id_service.get_union_id_by_user_id(str(current_user.id))
                
                if task_creator_union_id:
                    # 优先使用当前操作者的unionId作为operatorId，如果获取不到则使用任务创建者的unionId
                    operator_id = current_user_union_id if current_user_union_id else task_creator_union_id
                    
                    logger.info(f"🗑️ 删除钉钉待办任务: task_id={task.dingtalk_task_id}, user_id={task_creator_union_id}, operator_id={operator_id}")
                    
                    delete_result = await dingtalk_api.delete_todo_task(
                        user_id=task_creator_union_id,
                        task_id=task.dingtalk_task_id,
                        operator_id=operator_id
                    )
                    
                    if delete_result and delete_result.get("success"):
                        logger.info(f"✅ 成功从钉钉删除待办任务: {task.dingtalk_task_id}")
                    else:
                        logger.warning(f"⚠️ 从钉钉删除待办任务失败: {delete_result.get('message') if delete_result else '未知错误'}")
                else:
                    logger.warning(f"⚠️ 无法获取任务创建者unionId，跳过钉钉删除: task_user_id={task.user_id}")
            except Exception as e:
                logger.warning(f"💥 从钉钉删除待办任务异常: {str(e)}")
        
        # 软删除本地任务记录
        task.is_deleted = True
        task.deleted_at = datetime.utcnow()
        task.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(f"成功删除待办任务: {task_id}")
        
        return {
            "success": True,
            "message": "删除待办任务成功",
            "data": {
                "task_id": task_id,
                "title": task.title
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除钉钉待办任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除待办任务失败: {str(e)}"
        )

@router.post("/admin/todo-tasks/sync")
async def sync_todo_tasks(
    sync_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """同步钉钉待办任务状态（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_ORGANIZATION
        )
        
        # 获取钉钉配置 - 使用统一设置服务
        settings_service = UnifiedSettingsService(db, str(current_tenant.id), str(current_project.id))
        settings = await settings_service.get_settings()
        config = type('Config', (), settings)()
        
        if not config or not config.app_key or not config.app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，请先在插件设置中配置APP_KEY和APP_SECRET"
            )
        
        app_key = config.app_key
        app_secret = config.app_secret
        
        # 初始化服务
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        todo_service = TodoTaskSyncService(dingtalk_api, db)
        
        user_id = sync_data.get("user_id")
        if not user_id:
            # 如果没有指定用户ID，可以使用当前用户ID
            user_id = str(current_user.id)
            logger.info(f"未指定用户ID，使用当前用户ID: {user_id}")
        
        logger.info(f"准备同步用户 {user_id} 的待办任务")
        
        # 使用UnionIdService获取unionId - 这是唯一正确的方式
        union_id_service = UnionIdService(db, dingtalk_api, str(current_tenant.id), str(current_project.id))
        
        union_id = await union_id_service.get_union_id_by_user_id(user_id)
        
        if not union_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取用户的unionId，请确保用户已绑定钉钉账号"
            )
        
        logger.info(f"使用unionId: {union_id}，开始同步待办任务")
        
        # 使用unionId同步待办任务
        result = await todo_service.sync_todo_tasks_from_dingtalk(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            user_id=uuid.UUID(user_id),
            dingtalk_user_id=union_id
        )
        
        return {
            "success": result["success"],
            "message": result["message"],
            "data": {
                "synced_count": result.get("data", {}).get("synced_count", 0),
                "total_tasks": result.get("data", {}).get("total_tasks", 0),
                "errors": result.get("errors", [])
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步钉钉待办任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步待办任务失败: {str(e)}"
        )

@router.post("/admin/ai-integration")
async def configure_ai_integration(
    integration_config: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """配置AI集成（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_AI_INTEGRATION
        )
        
        # 查询或创建AI集成配置
        query = select(DingTalkAIIntegration).where(
            and_(
                DingTalkAIIntegration.tenant_id == current_tenant.id,
                DingTalkAIIntegration.project_id == current_project.id
            )
        )
        result = await db.execute(query)
        ai_integration = result.scalar_one_or_none()
        
        if ai_integration:
            # 更新现有配置
            ai_integration.integration_type = integration_config.get("integration_type", ai_integration.integration_type)
            ai_integration.ai_model_config = integration_config.get("ai_model_config", ai_integration.ai_model_config)
            ai_integration.auto_reply_enabled = integration_config.get("auto_reply_enabled", ai_integration.auto_reply_enabled)
            ai_integration.reply_templates = integration_config.get("reply_templates", ai_integration.reply_templates)
            ai_integration.trigger_keywords = integration_config.get("trigger_keywords", ai_integration.trigger_keywords)
            ai_integration.updated_at = datetime.now()
        else:
            # 创建新配置
            ai_integration = DingTalkAIIntegration(
                id=uuid.uuid4(),
                tenant_id=current_tenant.id,
                project_id=current_project.id,
                integration_type=integration_config.get("integration_type", "basic"),
                ai_model_config=integration_config.get("ai_model_config", {}),
                auto_reply_enabled=integration_config.get("auto_reply_enabled", False),
                reply_templates=integration_config.get("reply_templates", {}),
                trigger_keywords=integration_config.get("trigger_keywords", []),
                created_by=current_user.id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.add(ai_integration)
        
        await db.commit()
        
        return {
            "success": True,
            "message": "AI集成配置已保存",
            "data": {
                "id": str(ai_integration.id),
                "integration_type": ai_integration.integration_type,
                "auto_reply_enabled": ai_integration.auto_reply_enabled,
                "ai_model_config": ai_integration.ai_model_config,
                "reply_templates": ai_integration.reply_templates,
                "trigger_keywords": ai_integration.trigger_keywords
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置AI集成失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"配置AI集成失败: {str(e)}"
        )

@router.get("/admin/ai-integration")
async def get_ai_integration(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取AI集成配置（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_AI_INTEGRATION
        )
        
        # 查询AI集成配置
        query = select(DingTalkAIIntegration).where(
            and_(
                DingTalkAIIntegration.tenant_id == current_tenant.id,
                DingTalkAIIntegration.project_id == current_project.id
            )
        )
        result = await db.execute(query)
        ai_integration = result.scalar_one_or_none()
        
        if ai_integration:
            return {
                "success": True,
                "data": {
                    "id": str(ai_integration.id),
                    "integration_type": ai_integration.integration_type,
                    "auto_reply_enabled": ai_integration.auto_reply_enabled,
                    "ai_model_config": ai_integration.ai_model_config,
                    "reply_templates": ai_integration.reply_templates,
                    "trigger_keywords": ai_integration.trigger_keywords,
                    "created_at": ai_integration.created_at.isoformat() if ai_integration.created_at else None,
                    "updated_at": ai_integration.updated_at.isoformat() if ai_integration.updated_at else None
                }
            }
        else:
            return {
                "success": True,
                "data": {
                    "integration_type": "basic",
                    "auto_reply_enabled": False,
                    "ai_model_config": {},
                    "reply_templates": {},
                    "trigger_keywords": []
                }
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI集成配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI集成配置失败: {str(e)}"
        )

@router.post("/admin/advanced-sync")
async def advanced_dingtalk_sync(
    sync_config: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """高级钉钉数据同步（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.ADMIN_SYNC
        )
        
        # 获取访问令牌
        access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取钉钉访问令牌"
            )
        
        # 执行高级同步
        from ..utils.advanced_sync import DingTalkAdvancedSync
        advanced_sync = DingTalkAdvancedSync(access_token)
        
        sync_result = await advanced_sync.execute_sync(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            config=sync_config
        )
        
        return {
            "success": True,
            "message": "高级同步完成",
            "data": sync_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"高级钉钉数据同步失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"高级同步失败: {str(e)}"
        )

@router.get("/admin/system-status")
async def get_system_status(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取系统状态（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYSTEM_INTEGRATION
        )
        
        # 检查各项系统状态
        status_info = {
            "token_status": "unknown",
            "webhook_status": "unknown",
            "ai_integration_status": "unknown",
            "knowledge_sync_status": "unknown"
        }
        
        # 检查访问令牌状态
        try:
            access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
            status_info["token_status"] = "available" if access_token else "unavailable"
        except:
            status_info["token_status"] = "error"
        
        # 检查Webhook状态
        webhook_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id,
                DingTalkWebhook.enabled == True
            )
        )
        webhook_result = await db.execute(webhook_query)
        active_webhooks = len(webhook_result.scalars().all())
        status_info["webhook_status"] = "active" if active_webhooks > 0 else "inactive"
        
        # 检查AI集成状态
        ai_query = select(DingTalkAIIntegration).where(
            and_(
                DingTalkAIIntegration.tenant_id == current_tenant.id,
                DingTalkAIIntegration.project_id == current_project.id
            )
        )
        ai_result = await db.execute(ai_query)
        ai_integration = ai_result.scalar_one_or_none()
        status_info["ai_integration_status"] = "configured" if ai_integration else "not_configured"
        
        # 检查知识库同步状态
        kb_query = select(DingTalkKnowledgeBase).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        ).order_by(DingTalkKnowledgeBase.created_at.desc()).limit(1)
        kb_result = await db.execute(kb_query)
        latest_sync = kb_result.scalar_one_or_none()
        
        if latest_sync:
            status_info["knowledge_sync_status"] = latest_sync.sync_status
            status_info["last_sync_time"] = latest_sync.created_at.isoformat() if latest_sync.created_at else None
        else:
            status_info["knowledge_sync_status"] = "never_synced"
        
        return {
            "success": True,
            "data": status_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统状态失败: {str(e)}"
        )

# 这是一个内部函数，不再直接作为路由暴露
async def _perform_user_details_sync(
    user_id_to_sync: str,
    current_user_id: str,  # Keep for logging purposes
    db: AsyncSession,
    tenant_id: str,
    project_id: str
):
    """
    执行单个用户详细信息同步的核心逻辑。
    统一通过数据库映射查找 unionId，不再使用 'me'。
    """
    logger.info(f"执行用户详情同步: target_user={user_id_to_sync}, operator={current_user_id}")
    
    settings_service = UnifiedSettingsService(db, tenant_id, project_id)
    settings = await settings_service.get_settings()
    if not settings.get('app_key') or not settings.get('app_secret'):
        raise HTTPException(status_code=400, detail="请先配置钉钉应用密钥")

    dingtalk_api = DingTalkAPI(app_key=settings['app_key'], app_secret=settings['app_secret'])

    # 统一逻辑：必须从数据库的映射关系中查找 unionId
    logger.info(f"从数据库查找用户 {user_id_to_sync} 的 unionId...")
    mapping_query = select(DingTalkUserMapping).where(DingTalkUserMapping.user_id == uuid.UUID(user_id_to_sync))
    mapping_result = await db.execute(mapping_query)
    user_mapping = mapping_result.scalar_one_or_none()

    if not user_mapping or not user_mapping.dingtalk_union_id:
        logger.error(f"用户 {user_id_to_sync} 的钉钉绑定信息未在数据库中找到。")
        raise HTTPException(
            status_code=404, 
            detail=f"找不到用户 {user_id_to_sync} 的钉钉绑定信息。请确保该用户已完成钉钉账号绑定。"
        )
    
    union_id = user_mapping.dingtalk_union_id
    logger.info(f"成功从数据库为用户 {user_id_to_sync} 找到 unionId: {union_id}")

    # 调用封装好的高级方法，直接通过 unionId 获取用户详情
    user_detail = await dingtalk_api.get_user_info_by_unionid(union_id)
    
    if not user_detail:
        logger.error(f"钉钉API无法获取 unionId '{union_id}' 的用户详情。")
        raise HTTPException(status_code=404, detail=f"虽然找到了unionId，但无法从钉钉获取该用户的详细信息。UnionID: '{union_id}'")
    
    logger.info(f"成功获取用户 {user_id_to_sync} 的钉钉详情: {user_detail.get('name')}")
    
    # 在这里可以添加更新数据库的逻辑
    
    return {
        "user_id": user_id_to_sync,
        "union_id": user_detail.get('unionid'), # 返回真实的unionid
        "user_detail": user_detail
    }

@router.post("/admin/users/sync-mapping", response_model=SyncUserMappingSuccessResponse)
async def sync_user_mapping_from_platform(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """
    从平台主绑定关系同步当前用户的钉钉映射。
    用户在前端点击"获取个人详细信息"时调用。
    """
    try:
        # 1. 获取钉钉配置并初始化API
        dingtalk_config = await get_dingtalk_config(db, str(current_tenant.id), str(current_project.id))
        if not dingtalk_config.get("app_key") or not dingtalk_config.get("app_secret"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉应用未配置，请先在基础设置中完成配置。"
            )
        dingtalk_api = DingTalkAPI(dingtalk_config["app_key"], dingtalk_config["app_secret"])

        # 2. 初始化UnionIdService
        union_id_service = UnionIdService(db, dingtalk_api, str(current_tenant.id), str(current_project.id))

        # 3. 执行同步
        result = await union_id_service.sync_user_from_platform_binding(current_user)

        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("message", "同步失败")
            )
        
        # 4. 返回成功响应
        return {"success": True, "data": result["data"]}

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"同步用户钉钉映射时发生异常: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {e}"
        )

@router.get("/admin/users/current/details")
async def get_current_user_dingtalk_details(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户的钉钉绑定和插件映射信息"""
    try:
        # 查找平台绑定
        platform_binding_stmt = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.user_id == current_user.id,
                ThirdPartyAccount.platform == "dingtalk"
            )
        )
        platform_binding = await db.scalar(platform_binding_stmt)

        # 查找插件映射
        plugin_mapping_stmt = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.user_id == current_user.id,
                DingTalkUserMapping.project_id == current_project.id
            )
        )
        plugin_mapping = await db.scalar(plugin_mapping_stmt)

        return {
            "success": True,
            "data": {
                "user_id": current_user.id,
                "username": current_user.username,
                "third_party_account": {
                    "platform_user_id": platform_binding.platform_user_id if platform_binding else None,
                    "platform_username": platform_binding.platform_username if platform_binding else None,
                    "created_at": platform_binding.created_at if platform_binding else None,
                    "updated_at": platform_binding.updated_at if platform_binding else None,
                } if platform_binding else None,
                "dingtalk_mapping": plugin_mapping,
            }
        }
    except Exception as e:
        logger.error(f"获取用户钉钉详情失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/users/clear-mapping")
async def clear_user_mapping(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """清除当前用户的钉钉插件映射信息"""
    try:
        stmt = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.user_id == current_user.id,
                DingTalkUserMapping.project_id == current_project.id
            )
        )
        mapping = await db.scalar(stmt)
        if mapping:
            await db.delete(mapping)
            await db.commit()
            return {"success": True, "message": "用户映射已清除"}
        return {"success": True, "message": "没有找到需要清除的用户映射"}
    except Exception as e:
        logger.error(f"清除用户映射失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/create-project-users")
async def create_project_users_from_dingtalk(
    request_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """
    从钉钉用户批量创建项目用户（管理员功能）
    """
    try:
        dingtalk_user_ids = request_data.get("dingtalk_user_ids")
        role_id = request_data.get("role_id")
        store_id = request_data.get("store_id")

        if not dingtalk_user_ids or not role_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须提供钉钉用户ID和角色ID"
            )

        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_USERS
        )

        config = await get_dingtalk_config(db, str(current_tenant.id), str(current_project.id))
        if not config.get("app_key") or not config.get("app_secret"):
             raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉应用的AppKey或AppSecret未配置"
            )

        api = DingTalkAPI(
            app_key=config['app_key'], 
            app_secret=config['app_secret'], 
            agent_id=config.get('agent_id')
        )
        
        sync_service = DingTalkOrganizationSyncService(api, db)

        result = await sync_service.create_project_users_from_dingtalk(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            dingtalk_user_ids=dingtalk_user_ids,
            role_id=role_id,
            store_id=store_id,
            created_by=current_user.id
        )

        if result["success"]:
            await db.commit()
            return {"success": True, "message": "项目用户创建成功", "data": result}
        else:
            await db.rollback()
            return {"success": False, "message": "创建项目用户时发生错误", "data": result}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"一键创建项目用户失败: {str(e)}", exc_info=True)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"一键创建项目用户失败: {str(e)}"
        )

@router.get("/admin/roles")
async def get_project_roles(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取项目角色列表"""
    try:
        # 获取项目角色
        roles_query = select(Role).where(
            Role.project_id == current_project.id,
            Role.status == "active"
        ).order_by(Role.name)
        
        result = await db.execute(roles_query)
        roles = result.scalars().all()
        
        roles_data = []
        for role in roles:
            roles_data.append({
                "id": str(role.id),
                "name": role.name,
                "code": role.code,
                "description": role.description,
                "is_store_role": role.code in ["store_admin", "store_staff"]
            })
        
        return {
            "success": True,
            "data": roles_data
        }
        
    except Exception as e:
        logger.error(f"获取项目角色失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目角色失败: {str(e)}"
        )

@router.get("/admin/stores")
async def get_project_stores(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取项目下的门店列表"""
    try:
        stores_query = select(Store).where(Store.project_id == current_project.id)
        result = await db.execute(stores_query)
        stores = result.scalars().all()
        return {
            "success": True,
            "data": [
                {
                    "id": str(store.id), 
                    "name": store.name,
                    "code": store.code,
                    "address": store.address
                } for store in stores
            ]
        }
    except Exception as e:
        logger.error(f"获取项目门店列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取门店列表失败")

@router.post("/admin/sync-dingtalk-documents")
async def sync_dingtalk_documents_enhanced(
    sync_options: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """增强文档同步（支持深度递归获取、存储策略等）"""
    logger.info(f"🚀 开始增强钉钉文档同步，项目: {current_project.name}")
    logger.info(f"📋 同步选项: {sync_options}")
    
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_DOCUMENTS
        )
        
        # 获取钉钉配置
        config = await get_dingtalk_config(db, str(current_tenant.id), str(current_project.id))
        if not config["has_config"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉应用未配置，请先配置 App Key 和 App Secret"
            )
        
        # 初始化钉钉API
        dingtalk_api = DingTalkAPI(config["app_key"], config["app_secret"])
        
        # 初始化增强文档同步器
        document_sync = DocumentSyncService(dingtalk_api, db)
        
        logger.info("🔧 开始执行增强文档同步...")
        sync_start_time = time.time()
        
        # 执行增强文档同步
        result = await document_sync.sync_dingtalk_documents(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            sync_options=sync_options
        )
        
        sync_duration = time.time() - sync_start_time
        logger.info(f"⏱️ 增强文档同步完成，耗时: {sync_duration:.2f}秒")
        
        if result["success"]:
            logger.info(f"✅ 增强文档同步成功: {result['message']}")
            logger.info(f"📊 同步统计: {result['data']}")
        else:
            logger.error(f"❌ 增强文档同步失败: {result['message']}")
            if result["data"].get("errors"):
                logger.error(f"🚫 错误详情: {result['data']['errors']}")
        
        return {
            "success": result["success"],
            "message": result["message"],
            "data": result["data"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 增强文档同步异常: {str(e)}", exc_info=True)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"增强文档同步失败: {str(e)}"
        )

@router.post("/admin/users/resend-notification")
async def resend_notification(
    request_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """重发用户创建通知"""
    try:
        dingtalk_user_id = request_data.get("dingtalk_user_id")
        username = request_data.get("username")
        initial_password = request_data.get("initial_password")
        role_name = request_data.get("role_name")
        store_name = request_data.get("store_name")

        if not all([dingtalk_user_id, username, initial_password]):
            raise HTTPException(status_code=400, detail="缺少必要的参数")

        # 获取钉钉配置并初始化API
        config = await get_dingtalk_config(db, str(current_tenant.id), str(current_project.id))
        api = DingTalkAPI(
            app_key=config.get("app_key"),
            app_secret=config.get("app_secret"),
            agent_id=config.get("agent_id")
        )
        
        # 初始化服务并调用通知方法
        sync_service = DingTalkOrganizationSyncService(api, db)
        await sync_service._send_account_creation_notification(
            dingtalk_user_id=dingtalk_user_id,
            username=username,
            initial_password=initial_password,
            project_name=current_project.name,
            role_name=role_name,
            store_name=store_name
        )
        
        return {"success": True, "message": "通知已成功重发"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重发通知失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"重发通知失败: {str(e)}")

# ==================== 钉钉AI助理 直通模式 API (遵循插件独立原则) ====================
@router.post(
    "/dingtalk-ai/passthrough",
    summary="钉钉AI助理直通模式",
    description="接收钉钉AI助理的直通请求，调用主项目AI服务，并返回结果。",
    tags=["钉钉插件AI集成"],
    # 该接口由钉钉服务直接调用，不依赖于用户登录状态
    dependencies=[],
)
async def dingtalk_ai_passthrough_v2(
    token: str = Query(..., description="安全认证Token"),
    request_data: DingTalkAIPassthroughRequest = Body(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    钉钉AI助理直通模式端点。
    该端点由钉钉服务器调用，使用与项目插件配置绑定的安全令牌进行认证。
    它调用主项目的AI能力，但其自身逻辑和配置完全包含在插件内部。
    """
    project_id = project.id
    logger.info(f"收到钉钉AI直通请求V2，项目ID: {project_id}, Token: {token[:4]}****")
    
    try:
        # --- 1. 安全验证和配置获取 (插件内部) ---
        settings_service = UnifiedSettingsService(db, tenant_id=None, project_id=str(project_id))
        settings = await settings_service.get_settings()
        
        # 验证开关和Token
        if not settings.get("ai_passthrough_enabled"):
            logger.warning(f"项目 {project_id} 的AI直通模式未启用，拒绝访问。")
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Passthrough mode is not enabled.")

        passthrough_token = settings.get("ai_passthrough_token")
        if not passthrough_token or token != passthrough_token:
            logger.warning(f"项目 {project_id} 的AI直通模式Token验证失败。")
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Invalid access token.")

        # --- 2. 获取为直通模式指定的AI助手ID (插件内部) ---
        ai_assistant_id_str = settings.get("passthrough_ai_assistant_id")
        if not ai_assistant_id_str:
            logger.error(f"项目 {project_id} 已启用直通模式，但未在插件设置中指定AI助手。")
            raise HTTPException(
                status_code=status.HTTP_412_PRECONDITION_FAILED,
                detail="No AI assistant specified for passthrough mode in plugin settings."
            )
        
        ai_assistant_id = uuid.UUID(ai_assistant_id_str)
        logger.info(f"使用插件指定的AI助手ID: {ai_assistant_id}")

        # --- 3. 获取或创建对话线程 (核心逻辑) ---
        dingtalk_session_id = request_data.context.get("sessionId") if request_data.context else None
        thread = None

        if dingtalk_session_id:
            # 尝试根据钉钉sessionId查找现有线程
            thread_query = select(AIAssistantThread).where(
                and_(
                    AIAssistantThread.assistant_id == ai_assistant_id,
                    AIAssistantThread.metadata['dingtalk_session_id'] == dingtalk_session_id
                )
            )
            result = await db.execute(thread_query)
            thread = result.scalars().first()
            if thread:
                logger.info(f"找到现有对话线程: {thread.id} for dingtalk_session_id: {dingtalk_session_id}")

        if not thread:
            # 如果没找到，或钉钉没有提供sessionId，则创建新线程
            thread_create_data = AIAssistantThreadCreate(
                assistant_id=ai_assistant_id,
                title=f"钉钉直通对话 - {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
                metadata={"source": "dingtalk_passthrough", "dingtalk_session_id": dingtalk_session_id}
            )
            thread = await AIAssistantService.create_thread(db, thread_create_data)
            logger.info(f"创建新对话线程: {thread.id} for dingtalk_session_id: {dingtalk_session_id}")

        # --- 4. 调用主项目AI助手服务 (能力复用) ---
        chat_request = AIAssistantChatRequest(
            assistant_id=ai_assistant_id,
            thread_id=thread.id,
            content=request_data.query,
            content_type="text"
        )
        
        # 注意：这里调用的是高层的AIAssistantService
        chat_response = await AIAssistantService.chat_with_assistant(
            db=db,
            chat_request=chat_request,
            user_id=None, # 直通模式下可以没有内部用户
            project_id=project_id,
            is_shared=True
        )

        # --- 5. 处理并返回钉钉指定格式的结果 ---
        if chat_response and chat_response.get("message"):
            ai_content = chat_response["message"]
            
            response_payload = {
                "output": {
                    "text": ai_content
                },
                "context": {
                    "session_id": dingtalk_session_id,
                    "model_used": chat_response.get("usage", {}).get("model")
                }
            }
            logger.info(f"成功返回AI助手回复，长度: {len(ai_content)}")
            return response_payload
        else:
            logger.error(f"AI助手服务调用异常，返回空内容。项目ID: {project_id}")
            raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="AI service failed to respond.")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理钉钉AI直通请求时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )

