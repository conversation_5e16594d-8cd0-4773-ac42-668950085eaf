#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter

from .space import router as space_router
from .role import router as role_router
from .user import router as user_router
from .project_main import router as project_main_router
from .notification import router as notification_router
from .notification_settings import router as notification_settings_router
from .space_share import router as space_share_router
from .inventory_check import router as inventory_check_router
from .inventory_transfer import router as inventory_transfer_router
from .inventory import router as inventory_router
from .store_inventory_transfer import router as store_inventory_transfer_router
from .purchase_order import router as purchase_order_router
from .sales_report import router as sales_report_router
from .supplier import router as supplier_router
from .loss import router as loss_router
from .store import router as store_router
from .task import router as task_router
from .warehouse import router as warehouse_router
from .operation_log import router as operation_log_router
from .product import router as product_router
from .sales_management import router as sales_management_router
from .finance import router as finance_router
from .knowledge_base import router as knowledge_base_router
from .marketing_activity import router as marketing_activity_router
from .ai_chat import router as ai_chat_router
from .ai_embedding import router as ai_embedding_router
from .ai_vision import router as ai_vision_router
from .ai_audio import router as ai_audio_router
from .ai_knowledge import router as ai_knowledge_router
from .ai_assistant_chat import router as ai_assistant_chat_router
from .table_processing import router as table_processing_router
from .dashboard import router as dashboard_router
from .area_survey import router as area_survey_router
from .store_operations import router as store_operations_router
from .warehouse_inbound import router as warehouse_inbound_router
from .tasks import router as async_tasks_router
from .super_dashboard import router as super_dashboard_router

# 导入库存预警路由
from routers.inventory_alert import router as inventory_alert_router

# 导入经营分析路由
from api.project.business_analysis import router as business_analysis_router

# 创建项目路由器
router = APIRouter()

# 统一注册所有子路由，均加 /{project_id} 前缀
router.include_router(store_operations_router, prefix="/{project_id}/store-operations",tags=["门店运营"])
router.include_router(space_router, prefix="/{project_id}/space",tags=["空间管理"])
router.include_router(role_router, prefix="/{project_id}/roles",tags=["角色管理"])
router.include_router(user_router, prefix="/{project_id}/users",tags=["用户管理"])
router.include_router(project_main_router, prefix="/{project_id}")
router.include_router(notification_router, prefix="/{project_id}/notifications",tags=["通知管理"])
router.include_router(notification_settings_router, prefix="/{project_id}/notification-settings",tags=["通知设置"])
router.include_router(space_share_router, prefix="/{project_id}/space-share",tags=["文件分享"])

router.include_router(inventory_check_router, prefix="/{project_id}/inventory-checks")
router.include_router(inventory_transfer_router, prefix="/{project_id}/inventory-transfers")
router.include_router(store_inventory_transfer_router, prefix="/{project_id}/store-inventory-transfers",tags=["门店调拨"])

# 注册异步任务路由 - 必须在task_router之前，避免路由冲突
router.include_router(async_tasks_router, prefix="/{project_id}", tags=["异步任务"])

router.include_router(task_router, prefix="/{project_id}/tasks",tags=["任务管理"])
router.include_router(inventory_router, prefix="/{project_id}/inventories",tags=["仓库管理"])
router.include_router(inventory_alert_router, prefix="/{project_id}",tags=["库存预警"])
router.include_router(purchase_order_router, prefix="/{project_id}/purchase-orders",tags=["采购管理"])
router.include_router(sales_report_router, prefix="/{project_id}/sales-reports",tags=["日报管理"])
router.include_router(supplier_router, prefix="/{project_id}/suppliers",tags=["供应商管理"])
router.include_router(loss_router, prefix="/{project_id}/losses",tags=["损耗管理"])
router.include_router(store_router, prefix="/{project_id}/basic/stores",tags=["门店管理"])
router.include_router(warehouse_router, prefix="/{project_id}/basic/warehouses")
router.include_router(warehouse_inbound_router, prefix="/{project_id}/warehouses", tags=["仓库管理"])
router.include_router(operation_log_router, prefix="/{project_id}/operation-logs",tags=["操作日志"])
router.include_router(product_router, prefix="/{project_id}/products",tags=["产品管理"])
router.include_router(sales_management_router, prefix="/{project_id}/basic/sales",tags=["销售管理"])
router.include_router(finance_router, prefix="/{project_id}/finance",tags=["财务管理"])
router.include_router(knowledge_base_router, prefix="/{project_id}/knowledge-base",tags=["知识库管理"])
router.include_router(marketing_activity_router, prefix="/{project_id}", tags=["营销活动管理"])
router.include_router(area_survey_router, prefix="/{project_id}", tags=["周边调研"])
router.include_router(dashboard_router, prefix="/{project_id}",tags=["仪表盘"])
router.include_router(super_dashboard_router, prefix="/{project_id}/super-dashboard",tags=["超级大盘"])

# 注册 AI 路由
router.include_router(ai_chat_router, prefix="/{project_id}", tags=["AI"])
router.include_router(ai_embedding_router, prefix="/{project_id}", tags=["AI"])
router.include_router(ai_vision_router, prefix="/{project_id}", tags=["AI"])
router.include_router(ai_audio_router, prefix="/{project_id}", tags=["AI"])
router.include_router(ai_knowledge_router, prefix="/{project_id}", tags=["AI"])
router.include_router(ai_assistant_chat_router, prefix="/{project_id}", tags=["AI"])
router.include_router(table_processing_router, prefix="/{project_id}", tags=["AI"])

# 注册插件路由
from plugins import router as plugin_router

# 注册插件路由，使用 /{project_id}/plugin 前缀
# 注意：添加插件标签，这样插件路由只会出现在插件分类下
router.include_router(plugin_router, prefix="/{project_id}/plugin")

# 注册经营分析路由
router.include_router(business_analysis_router, prefix="/{project_id}/business-analysis", tags=["经营分析"])
