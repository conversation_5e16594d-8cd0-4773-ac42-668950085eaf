"""add store_sales_targets table

Revision ID: 0c5b3d6a6e7b
Revises: add_purchase_order_relation
Create Date: 2024-07-31 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0c5b3d6a6e7b'
down_revision = 'add_purchase_order_relation'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('store_sales_targets',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('store_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('target_year', sa.Integer(), nullable=False, comment='目标年份'),
        sa.Column('target_month', sa.Integer(), nullable=False, comment='目标月份'),
        sa.Column('last_year_sales', sa.Float(), nullable=True, comment='去年同期月销售额'),
        sa.Column('growth_rate', sa.Float(), nullable=True, comment='期望增长率(%)'),
        sa.Column('monthly_target', sa.Float(), nullable=False, comment='月度销售目标'),
        sa.Column('daily_target', sa.Float(), nullable=True, comment='日均销售目标'),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ondelete='SET NULL'),
        sa.UniqueConstraint('store_id', 'target_year', 'target_month', name='_store_year_month_uc')
    )
    op.create_index(op.f('ix_store_sales_targets_tenant_id'), 'store_sales_targets', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_store_sales_targets_project_id'), 'store_sales_targets', ['project_id'], unique=False)
    op.create_index(op.f('ix_store_sales_targets_store_id'), 'store_sales_targets', ['store_id'], unique=False)


def downgrade():
    op.drop_table('store_sales_targets') 