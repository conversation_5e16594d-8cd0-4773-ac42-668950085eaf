-- 门店销售目标表 (store_sales_targets)
CREATE TABLE IF NOT EXISTS store_sales_targets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    store_id UUID NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
    year INT NOT NULL,
    month INT NOT NULL,
    target_amount NUMERIC(15, 2) NOT NULL DEFAULT 0.00,
    achieved_amount NUMERIC(15, 2) NOT NULL DEFAULT 0.00,
    notes TEXT,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT uq_store_sales_target_per_month UNIQUE (project_id, store_id, year, month)
);

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_store_sales_targets_project_id ON store_sales_targets(project_id);
CREATE INDEX IF NOT EXISTS idx_store_sales_targets_store_id ON store_sales_targets(store_id);
CREATE INDEX IF NOT EXISTS idx_store_sales_targets_year_month ON store_sales_targets(year, month);

-- 添加注释
COMMENT ON TABLE store_sales_targets IS '用于存储各门店每月的销售目标';
COMMENT ON COLUMN store_sales_targets.id IS '唯一标识符';
COMMENT ON COLUMN store_sales_targets.project_id IS '关联的项目ID';
COMMENT ON COLUMN store_sales_targets.store_id IS '关联的门店ID';
COMMENT ON COLUMN store_sales_targets.year IS '目标年份';
COMMENT ON COLUMN store_sales_targets.month IS '目标月份';
COMMENT ON COLUMN store_sales_targets.target_amount IS '设定的目标销售额';
COMMENT ON COLUMN store_sales_targets.achieved_amount IS '已达成的销售额';
COMMENT ON COLUMN store_sales_targets.notes IS '备注信息';
COMMENT ON COLUMN store_sales_targets.created_by IS '创建人ID';
COMMENT ON COLUMN store_sales_targets.updated_by IS '更新人ID';
COMMENT ON COLUMN store_sales_targets.created_at IS '创建时间';
COMMENT ON COLUMN store_sales_targets.updated_at IS '更新时间';
COMMENT ON CONSTRAINT uq_store_sales_target_per_month ON store_sales_targets IS '确保每个门店每月只有一条目标记录'; 