#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import httpx
from datetime import datetime, date

async def test_api():
    """测试真实的门店KPI API"""
    print('🔧 测试真实的门店KPI API:')

    # 测试参数
    store_id = '2a8f1928-0d76-46cf-b87b-e83c6e1cc966'
    query_date = '2025-06-19'

    async with httpx.AsyncClient() as client:
        try:
            # 调用真实的API
            url = f"http://localhost:8000/api/v1/project/2a8f1928-0d76-46cf-b87b-e83c6e1cc966/super-dashboard/store-kpis"
            params = {
                'store_id': store_id,
                'date': query_date
            }

            response = await client.get(url, params=params)
            if response.status_code == 200:
                backend_data = response.json()
                print('✅ API调用成功')
                print(backend_data)
            else:
                print(f'❌ API调用失败，状态码: {response.status_code}')
                print(f'错误信息: {response.text}')
                # 使用模拟数据
                backend_data = {
                    'success': True,
                    'data': {
                        'sales': {'value': 4000.0, 'yoy': '+12.5%', 'mom': '+5.8%'},
                        'transactions': {'value': 456, 'yoy': '+8.2%', 'mom': '+3.1%'},
                        'avg_transaction_value': {'value': 28.2, 'yoy': '+4.1%', 'mom': '+2.6%'},
                        'loss_rate': {'value': 2.3, 'yoy': '-0.5%', 'mom': '-0.2%'},
                        'sales_target': {'total': 150000.0, 'completed': 4000.0}
                    }
                }
                print('使用模拟数据:')
                print(backend_data)
        except Exception as e:
            print(f'❌ API调用异常: {e}')
            # 使用模拟数据
            backend_data = {
                'success': True,
                'data': {
                    'sales': {'value': 4000.0, 'yoy': '+12.5%', 'mom': '+5.8%'},
                    'transactions': {'value': 456, 'yoy': '+8.2%', 'mom': '+3.1%'},
                    'avg_transaction_value': {'value': 28.2, 'yoy': '+4.1%', 'mom': '+2.6%'},
                    'loss_rate': {'value': 2.3, 'yoy': '-0.5%', 'mom': '-0.2%'},
                    'sales_target': {'total': 150000.0, 'completed': 4000.0}
                }
            }
            print('使用模拟数据:')
            print(backend_data)
        
        print('\n🎯 前端期望的转换结构:')
        frontend_expected = {
            'totalRevenue': backend_data['data'].get('totalRevenue', backend_data['data']['sales']),
            'totalLoss': backend_data['data'].get('totalLoss', backend_data['data']['loss_rate']),
            'customerTraffic': backend_data['data'].get('customerTraffic', backend_data['data']['transactions']),
            'salesTarget': backend_data['data'].get('salesTarget', backend_data['data']['sales_target'])
        }
        print(frontend_expected)

        print('\n✅ 问题分析:')
        if 'totalRevenue' in backend_data['data']:
            print('✅ 后端已返回正确的字段名 totalRevenue, totalLoss, customerTraffic, salesTarget')
            print('✅ 前端可以直接使用这些字段，无需转换')
        else:
            print('❌ 后端仍使用旧字段名，需要前端进行转换')
            print('❌ 建议后端直接返回前端期望的字段名')

if __name__ == "__main__":
    asyncio.run(test_api()) 