import React, { useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Switch,
  Button,
  Typography,
  Alert,
  message,
  Tooltip,
  Row,
  Col,
  Space,
  Modal,
  Select,
  Empty
} from 'antd';
import { CopyOutlined, RedoOutlined } from '@ant-design/icons';

const { Paragraph, Text } = Typography;

// ==================== AI集成标签页组件 ====================
const AIIntegration = ({ settings, onUpdateSettings, aiAssistants }) => {
  const [form] = Form.useForm();
  const projectId = localStorage.getItem('project_id');

  // 当外部settings更新时，同步到表单
  useEffect(() => {
    if (settings) {
      form.setFieldsValue({
        ai_passthrough_enabled: settings.ai_passthrough_enabled,
        passthrough_ai_assistant_id: settings.passthrough_ai_assistant_id,
      });
    }
  }, [settings, form]);

  const handleRegenerateToken = () => {
    Modal.confirm({
      title: '确认重新生成Token吗?',
      content: '重新生成Token后，您需要立即将新的直通URL更新到钉钉开发者后台，否则将导致AI助理服务中断。',
      okText: '确认生成',
      cancelText: '取消',
      onOk: () => {
        // 调用从父组件传入的更新函数
        onUpdateSettings({ regenerate_passthrough_token: true });
      },
    });
  };
  
  const passthroughUrl = settings?.ai_passthrough_token && projectId
    ? `${window.location.origin}/api/v1/project/${projectId}/plugin/dingtalk/dingtalk-ai/passthrough?token=${settings.ai_passthrough_token}`
    : null;

  return (
    <Card title="AI助理直通模式配置">
      <Row gutter={[16, 16]}>
        <Col span={24} md={12}>
          <Alert
            type="info"
            showIcon
            message="什么是直通模式？"
            description={
              <Typography>
                <Paragraph>
                  直通模式允许钉钉的AI助理直接调用本系统的AI大模型能力，实现无缝的智能问答体验。
                </Paragraph>
                <Paragraph>
                  开启后，您需要将下面生成的专属URL配置到钉钉开发者后台的 "AI助理-机器人-在线服务配置-直通地址" 中。
                </Paragraph>
              </Typography>
            }
          />
          <Form
            form={form}
            layout="vertical"
            onFinish={onUpdateSettings}
            style={{ marginTop: '24px' }}
          >
            <Form.Item
              name="ai_passthrough_enabled"
              label="启用直通模式"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="passthrough_ai_assistant_id"
              label="选择AI助手"
              rules={[{ required: true, message: '请为直通模式选择一个AI助手' }]}
              tooltip="此处的选择决定了钉钉AI助理具体使用哪个后台AI助手，包括其绑定的模型、知识库和工具等能力。"
            >
              <Select placeholder="请选择AI助手" allowClear>
                {(aiAssistants || []).map(assistant => (
                  <Select.Option key={assistant.id} value={assistant.id}>
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <div style={{ fontWeight: 'bold' }}>
                        {assistant.name}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {assistant.description || '暂无描述'}
                      </div>
                    </div>
                  </Select.Option>
                ))}
                {(!aiAssistants || aiAssistants.length === 0) && (
                  <Select.Option disabled value="">
                    <div style={{ color: '#999', fontSize: '12px' }}>
                      暂无可用的AI助手，请先在主应用的"AI能力-AI助理"页面创建
                    </div>
                  </Select.Option>
                )}
              </Select>
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
            </Form.Item>
          </Form>
        </Col>
        <Col span={24} md={12}>
          <Card type="inner" title="专属直通URL">
            {passthroughUrl ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text type="secondary">
                  将此URL复制到钉钉开发者后台。请注意，令牌 (token) 已包含在URL中，请勿泄露。
                </Text>
                <Input
                  value={passthroughUrl}
                  readOnly
                  addonAfter={
                    <Tooltip title="复制URL">
                      <Button
                        icon={<CopyOutlined />}
                        onClick={() => {
                          navigator.clipboard.writeText(passthroughUrl);
                          message.success('已复制到剪贴板');
                        }}
                      />
                    </Tooltip>
                  }
                />
                <Button
                  icon={<RedoOutlined />}
                  onClick={handleRegenerateToken}
                >
                  重新生成令牌
                </Button>
              </Space>
            ) : (
              <Empty
                description={
                  <span>
                    请先启用直通模式并保存设置，<br />
                    系统将在此处自动生成专属URL。
                  </span>
                }
              />
            )}
          </Card>
          
          {/* 内嵌的YAML配置显示区域 */}
          <Card type="inner" title="OpenAPI 配置 (YAML)" style={{ marginTop: 16 }}>
            <Paragraph type="secondary">
              将以下内容完整复制到钉钉开发者后台的"AI助理-能力-HTTP推送-接口协议"中，选择"YAML"格式进行导入。
            </Paragraph>
            <div style={{ position: 'relative', background: '#f5f5f5', borderRadius: '4px', padding: '16px', maxHeight: '300px', overflowY: 'auto' }}>
              <Tooltip title="复制YAML">
                <Button 
                    icon={<CopyOutlined />} 
                    style={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}
                    onClick={() => {
                        const yamlContent = getYamlContent(window.location.host, projectId);
                        navigator.clipboard.writeText(yamlContent);
                        message.success('YAML配置已复制到剪贴板');
                    }}
                />
              </Tooltip>
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
                  <code>{getYamlContent(window.location.host, projectId)}</code>
              </pre>
            </div>
          </Card>
        </Col>
      </Row>
    </Card>
  );
};

// 生成YAML内容的辅助函数
const getYamlContent = (domain, projectId) => {
  const finalDomain = domain || '{your_domain}';
  const finalProjectId = projectId || '{your_project_id}';

  return `openapi: 3.0.0
info:
  title: "钉钉AI助理直通模式API"
  version: "1.0.0"
  description: "用于接收钉钉AI助理的直通请求，调用内部AI服务并返回结果的API规范。"
servers:
  - url: "https://${finalDomain}/api/v1/project/${finalProjectId}/plugin/dingtalk"
paths:
  /dingtalk-ai/passthrough:
    post:
      summary: "钉钉AI助理直通模式"
      description: "接收钉钉AI助理的请求，调用后端AI大模型服务，并以指定格式返回结果。"
      tags:
        - "钉钉插件AI集成"
      parameters:
        - name: "token"
          in: "query"
          required: true
          description: "与插件后台配置中生成的安全认证Token保持一致"
          schema:
            type: "string"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DingTalkAIPassthroughRequest"
      responses:
        '200':
          description: "成功返回AI处理结果"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DingTalkAIPassthroughResponse"
        '403':
          description: "访问被拒绝。可能是Token错误或直通模式未启用。"
        '412':
          description: "预设条件失败。通常意味着插件后台未配置用于直通的AI模型。"
        '500':
          description: "服务器内部错误。"
        '503':
          description: "AI服务暂时不可用。"
components:
  schemas:
    DingTalkAIPassthroughRequest:
      type: "object"
      properties:
        query:
          type: "string"
          description: "用户的原始查询语句"
          example: "你好，帮我查一下最近的销售额"
        context:
          type: "object"
          description: "钉钉传入的上下文信息"
          properties:
            sessionId:
              type: "string"
              description: "会话ID"
      required:
        - "query"
    DingTalkAIPassthroughResponse:
      type: "object"
      properties:
        output:
          type: "object"
          properties:
            text:
              type: "string"
              description: "AI生成的回复文本"
              example: "好的，正在为您查询最近的销售额..."
        context:
          type: "object"
          properties:
            session_id:
              type: "string"
              description: "需要原样返回的会话ID"
            model_used:
              type: "string"
              description: "本次调用实际使用的AI模型"
              example: "deepseek-chat"
`;
};

export default AIIntegration; 