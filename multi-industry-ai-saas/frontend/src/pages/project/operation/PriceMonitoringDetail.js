import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Typography,
  Space,
  Button,
  Select,
  DatePicker,
  Statistic,
  Tag,
  message,
  Spin,
  Modal,
  Input,
  Divider,
  Alert
} from 'antd';
import {
  LineChartOutlined,
  RobotOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  TrendingUpOutlined,
  <PERSON>CircleOutlined,
  WarningOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { Line, Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import { useNavigate, useLocation } from 'react-router-dom';
import superDashboardService from '../../../services/api/project/superDashboard';
import aiService from '../../../services/api/project/ai';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const PriceMonitoringDetail = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [aiLoading, setAiLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  const [dateRange, setDateRange] = useState([dayjs().subtract(7, 'day'), dayjs()]);
  const [priceData, setPriceData] = useState([]);
  const [trendData, setTrendData] = useState([]);
  const [summaryStats, setSummaryStats] = useState({});
  const [aiPredictionVisible, setAiPredictionVisible] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [aiPrediction, setAiPrediction] = useState('');

  // 从URL参数或localStorage获取门店ID
  const getStoreId = () => {
    const urlParams = new URLSearchParams(location.search);
    return urlParams.get('storeId') || localStorage.getItem('selected_store_id');
  };

  const storeId = getStoreId();

  useEffect(() => {
    if (storeId) {
      fetchPriceMonitoringData();
    }
  }, [storeId, selectedPeriod, dateRange]);

  const fetchPriceMonitoringData = async () => {
    setLoading(true);
    try {
      const [priceRes, trendRes] = await Promise.all([
        superDashboardService.operation.getPriceMonitoring(storeId, selectedPeriod),
        superDashboardService.operation.getPriceTrends(storeId, {
          start_date: dateRange[0].format('YYYY-MM-DD'),
          end_date: dateRange[1].format('YYYY-MM-DD')
        })
      ]);

      if (priceRes.success) {
        setPriceData(priceRes.data.price_trends || []);
        setSummaryStats(priceRes.data.summary || {});
      }

      if (trendRes.success) {
        setTrendData(trendRes.data || []);
      }
    } catch (error) {
      console.error('获取价格监控数据失败:', error);
      message.error('获取价格监控数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAIPrediction = async () => {
    if (!customPrompt.trim()) {
      message.warning('请输入分析需求');
      return;
    }

    setAiLoading(true);
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        message.error('项目ID不存在');
        return;
      }

      const prompt = `
基于以下价格监控数据进行分析和预测：

门店ID: ${storeId}
时间范围: ${dateRange[0].format('YYYY-MM-DD')} 至 ${dateRange[1].format('YYYY-MM-DD')}
监控周期: ${selectedPeriod}

价格统计数据:
- 平均波动率: ${summaryStats.avg_volatility}%
- 高波动产品数: ${summaryStats.high_volatility_count}
- 监控产品总数: ${summaryStats.total_products}

用户需求: ${customPrompt}

请提供专业的价格走势分析和预测建议。
      `;

      const response = await aiService.chatWithAssistant(projectId, {
        message: prompt,
        context: {
          type: 'price_monitoring',
          store_id: storeId,
          data: { priceData, trendData, summaryStats }
        }
      });

      if (response.success) {
        setAiPrediction(response.data.message || response.message || '分析完成');
        message.success('AI分析完成');
      } else {
        message.error('AI分析失败');
      }
    } catch (error) {
      console.error('AI分析失败:', error);
      message.error('AI分析失败');
    } finally {
      setAiLoading(false);
    }
  };

  const renderComparison = (value, unit = '%') => {
    if (value === 0 || value === null || value === undefined) return <Text style={{ color: '#8c8c8c' }}>-</Text>;
    const color = value > 0 ? '#cf1322' : '#3f8600';
    const icon = value > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />;
    return (
      <span style={{ color }}>
        {icon} {Math.abs(value)}{unit}
      </span>
    );
  };

  const priceColumns = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      fixed: 'left',
      width: 150
    },
    {
      title: '当前价格',
      dataIndex: 'avg_price',
      key: 'avg_price',
      render: (text) => `¥${parseFloat(text).toFixed(2)}`,
      sorter: (a, b) => a.avg_price - b.avg_price
    },
    {
      title: '价格区间',
      key: 'price_range',
      render: (_, record) => `¥${parseFloat(record.min_price).toFixed(2)} - ¥${parseFloat(record.max_price).toFixed(2)}`
    },
    {
      title: '波动率',
      dataIndex: 'price_volatility',
      key: 'price_volatility',
      render: (text) => {
        const volatility = parseFloat(text);
        const color = volatility > 10 ? '#ff4d4f' : volatility > 5 ? '#faad14' : '#52c41a';
        return <span style={{ color }}>{volatility.toFixed(1)}%</span>;
      },
      sorter: (a, b) => a.price_volatility - b.price_volatility
    },
    {
      title: '较昨日',
      dataIndex: 'daily_change',
      key: 'daily_change',
      render: (text) => renderComparison(text)
    },
    {
      title: '较上周',
      dataIndex: 'weekly_change',
      key: 'weekly_change',
      render: (text) => renderComparison(text)
    },
    {
      title: '到货次数',
      dataIndex: 'arrival_count',
      key: 'arrival_count',
      sorter: (a, b) => a.arrival_count - b.arrival_count
    },
    {
      title: '风险等级',
      key: 'risk_level',
      render: (_, record) => {
        const volatility = parseFloat(record.price_volatility);
        if (volatility > 15) return <Tag color="red">高风险</Tag>;
        if (volatility > 8) return <Tag color="orange">中风险</Tag>;
        return <Tag color="green">低风险</Tag>;
      }
    }
  ];

  // 价格趋势图表配置
  const trendChartConfig = {
    data: trendData,
    xField: 'date',
    yField: 'price',
    seriesField: 'product_name',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    legend: {
      position: 'top',
    },
    tooltip: {
      formatter: (datum) => {
        return {
          name: datum.product_name,
          value: `¥${datum.price.toFixed(2)}`
        };
      }
    }
  };

  // 波动率分布图表配置
  const volatilityChartConfig = {
    data: priceData.map(item => ({
      product_name: item.product_name,
      volatility: parseFloat(item.price_volatility)
    })),
    xField: 'product_name',
    yField: 'volatility',
    color: ({ volatility }) => {
      if (volatility > 10) return '#ff4d4f';
      if (volatility > 5) return '#faad14';
      return '#52c41a';
    },
    label: {
      position: 'top',
      formatter: (text) => `${text}%`
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ backgroundColor: '#fff', padding: '24px' }}>
        {/* 页面头部 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <div>
            <Title level={3} style={{ margin: 0 }}>价格监控详情</Title>
            <Text type="secondary">深度分析产品价格波动趋势，提供智能预测建议</Text>
          </div>
          <Space>
            <Select
              value={selectedPeriod}
              onChange={setSelectedPeriod}
              style={{ width: 120 }}
            >
              <Option value="day">日监控</Option>
              <Option value="week">周监控</Option>
              <Option value="month">月监控</Option>
            </Select>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              format="YYYY-MM-DD"
            />
            <Button onClick={fetchPriceMonitoringData} loading={loading}>刷新数据</Button>
            <Button type="primary" onClick={() => navigate(-1)}>返回</Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          {/* 统计概览 */}
          <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="监控产品数"
                  value={summaryStats.total_products || 0}
                  prefix={<BarChartOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="平均波动率"
                  value={summaryStats.avg_volatility || 0}
                  suffix="%"
                  prefix={<TrendingUpOutlined />}
                  valueStyle={{ 
                    color: (summaryStats.avg_volatility || 0) > 10 ? '#cf1322' : '#3f8600' 
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="高波动产品"
                  value={summaryStats.high_volatility_count || 0}
                  prefix={<WarningOutlined />}
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="平均价格"
                  value={summaryStats.avg_price || 0}
                  precision={2}
                  prefix={<DollarCircleOutlined />}
                  suffix="元"
                />
              </Card>
            </Col>
          </Row>

          {/* 价格趋势图表 */}
          <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <LineChartOutlined />
                    价格趋势分析
                  </Space>
                }
                extra={
                  <Button
                    type="primary"
                    ghost
                    icon={<RobotOutlined />}
                    onClick={() => setAiPredictionVisible(true)}
                  >
                    AI 价格预测
                  </Button>
                }
              >
                {trendData.length > 0 ? (
                  <Line {...trendChartConfig} height={300} />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <Text type="secondary">暂无趋势数据</Text>
                  </div>
                )}
              </Card>
            </Col>
          </Row>

          {/* 波动率分布图 */}
          <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <BarChartOutlined />
                    产品波动率分布
                  </Space>
                }
              >
                {priceData.length > 0 ? (
                  <Column {...volatilityChartConfig} height={300} />
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <Text type="secondary">暂无波动率数据</Text>
                  </div>
                )}
              </Card>
            </Col>
          </Row>

          {/* 详细数据表格 */}
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Card
                title="产品价格详情"
                extra={
                  <Space>
                    <Text type="secondary">共 {priceData.length} 个产品</Text>
                  </Space>
                }
              >
                <Table
                  dataSource={priceData}
                  columns={priceColumns}
                  rowKey={(record) => `${record.product_name}-${record.arrival_date}`}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }}
                  scroll={{ x: 1200 }}
                />
              </Card>
            </Col>
          </Row>
        </Spin>

        {/* AI 价格预测模态框 */}
        <Modal
          title={
            <Space>
              <RobotOutlined />
              AI 价格走势预测
            </Space>
          }
          open={aiPredictionVisible}
          onCancel={() => setAiPredictionVisible(false)}
          width={800}
          footer={[
            <Button key="cancel" onClick={() => setAiPredictionVisible(false)}>
              关闭
            </Button>,
            <Button
              key="predict"
              type="primary"
              loading={aiLoading}
              onClick={handleAIPrediction}
            >
              开始分析
            </Button>
          ]}
        >
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Alert
              message="AI 价格预测"
              description="基于历史价格数据和市场趋势，AI将为您提供专业的价格走势分析和预测建议。"
              type="info"
              showIcon
            />

            <div>
              <Text strong>分析需求:</Text>
              <TextArea
                value={customPrompt}
                onChange={(e) => setCustomPrompt(e.target.value)}
                placeholder="请描述您的分析需求，例如：分析未来一周的价格趋势、识别价格异常波动的原因、提供采购建议等..."
                rows={4}
                style={{ marginTop: 8 }}
              />
            </div>

            {aiPrediction && (
              <div>
                <Divider />
                <Text strong>AI 分析结果:</Text>
                <div style={{
                  marginTop: 8,
                  padding: 16,
                  backgroundColor: '#f6ffed',
                  border: '1px solid #b7eb8f',
                  borderRadius: 6,
                  whiteSpace: 'pre-wrap'
                }}>
                  {aiPrediction}
                </div>
              </div>
            )}
          </Space>
        </Modal>
      </div>
    </div>
  );
};

export default PriceMonitoringDetail;
