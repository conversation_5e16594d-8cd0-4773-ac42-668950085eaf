import React, { useState } from 'react';
import {
  Card,
  Form,
  Select,
  InputNumber,
  Button,
  Row,
  Col,
  Statistic,
  Divider,
  Table,
  message,
  Upload,
  Tabs,
  Modal,
} from 'antd';
import { 
    CalculatorOutlined, 
    SaveOutlined,
    InboxOutlined,
    DownloadOutlined,
    UploadOutlined,
} from '@ant-design/icons';
import FileSpaceSelector from '../../../../components/FileSpaceSelector';

const { Option } = Select;
const { TabPane } = Tabs;
const { Dragger } = Upload;

// Mock data for existing targets
const mockTargets = [
    {
        key: 'store-001',
        storeId: 'store-001',
        storeName: '总店',
        lastYearSales: 320000,
        growthRate: 15,
        monthlyTarget: 368000,
        dailyTarget: 12267,
    },
    {
        key: 'store-002',
        storeId: 'store-002',
        storeName: '滨江店',
        lastYearSales: 250000,
        growthRate: 20,
        monthlyTarget: 300000,
        dailyTarget: 10000,
    }
];

const StoreTargetManagement = ({ stores }) => {
  const [form] = Form.useForm();
  const [calculatedTarget, setCalculatedTarget] = useState(null);
  const [targets, setTargets] = useState(mockTargets);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [isSpaceModalVisible, setIsSpaceModalVisible] = useState(false);

  const handleCalculate = () => {
    form.validateFields(['lastYearSales', 'growthRate']).then(values => {
      const { lastYearSales, growthRate } = values;
      if(!lastYearSales || !growthRate) {
        message.error('请输入销售额和增长率');
        return;
      }
      const monthlyTarget = lastYearSales * (1 + growthRate / 100);
      const dailyTarget = monthlyTarget / 30; // Assume 30 days for simplicity
      setCalculatedTarget({
        monthlyTarget: monthlyTarget.toFixed(2),
        dailyTarget: dailyTarget.toFixed(2),
      });
    });
  };

  const handleSave = () => {
    form.validateFields().then(values => {
        if (!calculatedTarget) {
            message.error('请先计算目标');
            return;
        }
        setLoading(true);
        console.log('Saving target:', { ...values, ...calculatedTarget });
        // Mock API call
        setTimeout(() => {
            const newTarget = {
                key: values.storeId,
                storeId: values.storeId,
                storeName: stores.find(s => s.id === values.storeId)?.name || '未知门店',
                ...values,
                monthlyTarget: parseFloat(calculatedTarget.monthlyTarget),
                dailyTarget: parseFloat(calculatedTarget.dailyTarget),
            };
            
            const newTargets = targets.filter(t => t.storeId !== newTarget.storeId);
            newTargets.push(newTarget);
            
            setTargets(newTargets.sort((a,b) => a.storeName.localeCompare(b.storeName)));
            setLoading(false);
            message.success('目标保存成功');
            form.resetFields();
            setCalculatedTarget(null);
        }, 500);
    });
  };

  const handleUpload = async (options) => {
    const { file } = options;
    setUploading(true);
    
    try {
      //
      // 此处应调用apiService.project.storeOperations.uploadTargets(file)
      // 由于后端尚未完全实现，暂时模拟
      await new Promise(resolve => setTimeout(resolve, 1500));
      setFileList([]);
      message.success(`${file.name} 文件上传成功并处理完毕.`);
    } catch (error) {
      message.error(`${file.name} 文件上传失败.`);
    } finally {
      setUploading(false);
    }
  };

  const handleSpaceFileSelect = async (selectedFile) => {
    if (!selectedFile) {
      setIsSpaceModalVisible(false);
      return;
    }
    
    setIsSpaceModalVisible(false);
    message.info(`已选择文件: ${selectedFile.name}，开始处理...`);
    
    // 模拟从文件空间获取文件并上传
    setUploading(true);
    try {
      // 此处应调用apiService下载文件，然后调用上传接口
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success(`文件 ${selectedFile.name} 处理成功！`);
    } catch (error) {
      message.error(`处理文件 ${selectedFile.name} 失败。`);
    } finally {
      setUploading(false);
    }
  };

  const handleDownloadTemplate = () => {
    message.info('正在准备模板，请稍候...');
    // 此处应生成并下载一个CSV模板
  };

  const draggerProps = {
    name: 'file',
    multiple: false,
    fileList,
    beforeUpload: (file) => {
      // 阻止自动上传，手动处理
      handleUpload({ file });
      return false;
    },
    onChange: (info) => {
        setFileList([info.fileList[info.fileList.length - 1]]);
    },
    accept: ".csv,.xlsx,.xls",
  };
  
  const columns = [
      { title: '门店名称', dataIndex: 'storeName', key: 'storeName' },
      { title: '去年同期月销售 (元)', dataIndex: 'lastYearSales', key: 'lastYearSales', render: (val) => val.toLocaleString() },
      { title: '期望增长率 (%)', dataIndex: 'growthRate', key: 'growthRate' },
      { title: '月度目标 (元)', dataIndex: 'monthlyTarget', key: 'monthlyTarget', render: (val) => <span style={{color: '#1890ff', fontWeight: 'bold'}}>{val.toLocaleString()}</span> },
      { title: '日均目标 (元)', dataIndex: 'dailyTarget', key: 'dailyTarget', render: (val) => val.toLocaleString() },
      { title: '操作', key: 'action', render: (_, record) => (
          <Button type="link" onClick={() => {
              form.setFieldsValue(record);
              setCalculatedTarget(null);
          }}>编辑</Button>
      )},
  ];

  return (
    <div>
      <Row gutter={24}>
        <Col span={12}>
          <Card title="手动设置销售目标">
            <Form form={form} layout="vertical" onValuesChange={() => setCalculatedTarget(null)}>
              <Form.Item name="storeId" label="选择门店" rules={[{ required: true, message: '请选择门店' }]}>
                <Select placeholder="请选择门店" allowClear>
                    {(stores || []).map(store => (
                        <Option key={store.id} value={store.id}>{store.name}</Option>
                    ))}
                </Select>
              </Form.Item>
              <Form.Item name="lastYearSales" label="去年同期月销售额 (元)" rules={[{ required: true, message: '请输入销售额' }]}>
                <InputNumber style={{ width: '100%' }} placeholder="例如: 300000" min={0} />
              </Form.Item>
              <Form.Item name="growthRate" label="期望增长率 (%)" rules={[{ required: true, message: '请输入增长率' }]}>
                <InputNumber style={{ width: '100%' }} placeholder="例如: 15" min={0} />
              </Form.Item>
              <Form.Item>
                <Button type="dashed" icon={<CalculatorOutlined />} onClick={handleCalculate} block>
                  计算目标
                </Button>
              </Form.Item>

              {calculatedTarget && (
                <div>
                  <Divider>计算结果</Divider>
                  <Row gutter={16}>
                    <Col span={12}>
                        <Statistic title="月度销售目标" value={calculatedTarget.monthlyTarget} prefix="¥" />
                    </Col>
                    <Col span={12}>
                        <Statistic title="日均销售目标" value={calculatedTarget.dailyTarget} prefix="¥" />
                    </Col>
                  </Row>
                  <Divider />
                  <Button type="primary" icon={<SaveOutlined />} onClick={handleSave} loading={loading} block>
                    保存目标
                  </Button>
                </div>
              )}
            </Form>
          </Card>
        </Col>
        <Col span={12}>
            <Card title="智能上传销售目标">
                <Tabs defaultActiveKey="upload">
                    <TabPane tab="本地上传" key="upload">
                        <Dragger {...draggerProps} disabled={uploading}>
                            <p className="ant-upload-drag-icon">
                                <InboxOutlined />
                            </p>
                            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                            <p className="ant-upload-hint">
                                支持上传包含门店ID、去年同期销售额、期望增长率等信息的文件 (CSV, XLSX, XLS)。
                            </p>
                        </Dragger>
                    </TabPane>
                    <TabPane tab="从项目空间选择" key="2">
                        <div style={{ padding: '20px', textAlign: 'center' }}>
                            <Button 
                              type="primary" 
                              icon={<InboxOutlined />}
                              onClick={() => setIsSpaceModalVisible(true)}
                              loading={uploading}
                            >
                              选择项目文件
                            </Button>
                            <p style={{ marginTop: '10px', color: '#888' }}>从当前项目的共享空间选择已有的目标文件进行上传。</p>
                        </div>
                    </TabPane>
                </Tabs>
                <Divider />
                <Button 
                    icon={<DownloadOutlined />}
                    onClick={handleDownloadTemplate}
                    block
                >
                    下载模板
                </Button>
            </Card>
        </Col>
      </Row>
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
            <Card title="门店目标总览">
                <Table
                    columns={columns}
                    dataSource={targets}
                    pagination={{ pageSize: 5 }}
                    rowKey="key"
                    loading={loading}
                />
            </Card>
        </Col>
      </Row>

      <Modal
        title="从项目空间选择文件"
        open={isSpaceModalVisible}
        onCancel={() => setIsSpaceModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <FileSpaceSelector onSelect={handleSpaceFileSelect} />
      </Modal>
    </div>
  );
};

export default StoreTargetManagement; 