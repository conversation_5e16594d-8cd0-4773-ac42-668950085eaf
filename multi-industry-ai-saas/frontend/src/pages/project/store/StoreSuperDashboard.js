import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  DatePicker,
  Button,
  Table,
  Typography,
  Tag,
  Space,
  Tooltip,
  Select,
  Radio,
  List,
  Progress,
  message,
  Spin,
  Divider,
  Modal,
  Skeleton,
} from 'antd';
import {
  Line<PERSON>hartOutlined,
  Area<PERSON><PERSON>Outlined,
  BarC<PERSON>Outlined,
  Pie<PERSON>hartOutlined,
  InfoCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  RobotOutlined,
  CloudOutlined,
  ReadOutlined,
  TagOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  AimOutlined,
  ShopOutlined,
  SyncOutlined,
  AlertOutlined,
  DollarCircleOutlined,
  EnvironmentOutlined,
  UserOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import apiService from '../../../services/api';

const { Title, Paragraph, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const StoreSuperDashboard = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [selectedStore, setSelectedStore] = useState(null);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [stores, setStores] = useState([]);
  
  // Data states
  const [kpiData, setKpiData] = useState({
    totalRevenue: { value: 0, yoy: 0, mom: 0 },
    totalLoss: { value: 0, yoy: 0, mom: 0 },
    customerTraffic: { value: 0, yoy: 0, mom: 0 },
    salesTarget: { total: 100000, completed: 0 },
  });
  const [pricePowerData, setPricePowerData] = useState([]);
  const [lossRankData, setLossRankData] = useState([]);
  const [keyProductData, setKeyProductData] = useState([]);
  const [weatherData, setWeatherData] = useState(null);
  const [industryNews, setIndustryNews] = useState([]);
  const [salesTarget, setSalesTarget] = useState(null);
  const [inventoryAlerts, setInventoryAlerts] = useState([]);
  const [staleProducts, setStaleProducts] = useState([]);
  const [newsLoading, setNewsLoading] = useState(false);
  const [isAssistantModalVisible, setIsAssistantModalVisible] = useState(false);
  const [assistants, setAssistants] = useState([]);
  const [priceMonitoringData, setPriceMonitoringData] = useState([]);
  const [priceMonitoringPeriod, setPriceMonitoringPeriod] = useState('week');

  const fetchAllDashboardData = useCallback(async () => {
    if (!selectedStore || !selectedDate) return;
    setLoading(true);
    try {
      const dateStr = selectedDate.format('YYYY-MM-DD');
      const [kpiRes, pricePowerRes, lossRankRes, keyProductRes, weatherRes, priceMonitoringRes] = await Promise.all([
        apiService.project.superDashboard.getStoreKpis(selectedStore, dateStr),
        apiService.project.superDashboard.getPricePower(selectedStore, dateStr),
        apiService.project.superDashboard.getLossRank(selectedStore, dateStr),
        apiService.project.superDashboard.getKeyProducts(selectedStore, dateStr),
        apiService.project.superDashboard.getWeather(selectedStore),
        apiService.project.superDashboard.getPriceMonitoring(selectedStore, priceMonitoringPeriod),
      ]);

      if (kpiRes && kpiRes.success) {
        console.log('🔍 KPI API 返回的完整数据:', kpiRes);
        console.log('🔍 KPI data 字段:', kpiRes.data);

        // 直接使用后端返回的正确字段名，无需转换
        const transformedKpiData = {
          totalRevenue: kpiRes.data.totalRevenue || { value: 0, yoy: 0, mom: 0 },
          totalLoss: kpiRes.data.totalLoss || { value: 0, yoy: 0, mom: 0 },
          customerTraffic: kpiRes.data.customerTraffic || { value: 0, yoy: 0, mom: 0 },
          salesTarget: kpiRes.data.salesTarget || { total: 100000, completed: 0 },
        };
        console.log('🔍 转换后的KPI数据:', transformedKpiData);
        setKpiData(transformedKpiData);
      }
      
      if (pricePowerRes && pricePowerRes.success) {
        console.log('💰 价格力 API 返回的完整数据:', pricePowerRes);
        const rawData = pricePowerRes.data;
        let processedData = [];
        
        if (Array.isArray(rawData)) {
          processedData = rawData;
        } else if (rawData && Array.isArray(rawData.data)) {
          processedData = rawData.data;
        }
        
        console.log('💰 处理后的价格力数据:', processedData);
        setPricePowerData(processedData);
      }

      if (lossRankRes && lossRankRes.success) {
        console.log('📉 损耗排行 API 返回的数据:', lossRankRes);
        setLossRankData(Array.isArray(lossRankRes.data) ? lossRankRes.data : []);
      }
      
      if (keyProductRes && keyProductRes.success) {
        console.log('🏆 重点单品 API 返回的数据:', keyProductRes);
        setKeyProductData(Array.isArray(keyProductRes.data) ? keyProductRes.data : []);
      }
      
      if (weatherRes && weatherRes.success) {
        console.log('🌤️ 天气 API 返回的数据:', weatherRes);
        setWeatherData(weatherRes.data);
      }

      if (priceMonitoringRes && priceMonitoringRes.success) {
        console.log('💰 价格监控 API 返回的数据:', priceMonitoringRes);
        setPriceMonitoringData(priceMonitoringRes.data);
      }

    } catch (error) {
      console.error("获取大盘数据失败", error);
      message.error("加载大盘数据失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  }, [selectedStore, selectedDate]);

  useEffect(() => {
    fetchStores();
  }, []);

  useEffect(() => {
    fetchAllDashboardData();
  }, [selectedStore, selectedDate, fetchAllDashboardData]);

  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
        if (response.items.length > 0) {
          setSelectedStore(response.items[0].id);
        }
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  const handlePriceAdjusted = async (record) => {
    try {
      await apiService.project.store.updatePriceStatus(record.id, { status: 'adjusted' });
      message.success('状态更新成功');
      // Re-fetch price power data
      const pricePowerRes = await apiService.project.superDashboard.getPricePower(selectedStore, selectedDate.format('YYYY-MM-DD'));
      if (pricePowerRes.success) setPricePowerData(pricePowerRes.data);
    } catch (error) {
      message.error('更新价格状态失败');
    }
  };

  const renderComparison = (value, unit = '%') => {
    if (value === null || value === undefined) {
      return <span style={{color: '#888'}}>-</span>
    }
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return <span style={{color: '#888'}}>{value}</span>
    }
    const color = numValue > 0 ? '#cf1322' : '#3f8600';
    const icon = numValue > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />;
    return (
      <span style={{ color }}>
        {icon} {Math.abs(numValue).toFixed(1)}{unit}
      </span>
    );
  };

  const topProductsColumns = [
    { title: '排名', dataIndex: 'key', key: 'key', render: text => <strong>{text}</strong> },
    { title: '产品名称', dataIndex: 'name', key: 'name' },
    { title: '销售额', dataIndex: 'sales', key: 'sales' },
    { title: '销售占比', dataIndex: 'proportion', key: 'proportion' },
    { title: '操作', key: 'action', render: () => <Button type="link" size="small">分析</Button> },
  ];

  const lossRankColumns = [
    { title: '排名', dataIndex: 'key', key: 'key', render: text => <strong>{text}</strong> },
    { title: '损耗产品/品类', dataIndex: 'name', key: 'name' },
    { title: '报损金额', dataIndex: 'loss', key: 'loss' },
    { title: '主要原因', dataIndex: 'reason', key: 'reason' },
    { title: '操作', key: 'action', render: () => <Button type="link" size="small">分析</Button> },
  ];

  const pricePowerColumns = [
    { title: '商品名称', dataIndex: 'name', key: 'name' },
    { 
      title: '我方售价', 
      dataIndex: 'our_price', 
      key: 'our_price', 
      render: (text) => {
        const price = parseFloat(text);
        return isNaN(price) ? '¥0.00' : `¥${price.toFixed(2)}`;
      }
    },
    { 
      title: '市场均价', 
      dataIndex: 'market_price', 
      key: 'market_price', 
      render: (text) => {
        const price = parseFloat(text);
        return isNaN(price) ? '¥0.00' : `¥${price.toFixed(2)}`;
      }
    },
    {
      title: '价格差异',
      dataIndex: 'price_diff',
      key: 'price_diff',
      render: (text) => {
        const diff = parseFloat(text);
        if (isNaN(diff)) {
          return <Text>¥0.00</Text>;
        }
        if (diff > 0) {
          return <Text type="danger"><ArrowUpOutlined /> ¥{diff.toFixed(2)}</Text>;
        } else if (diff < 0) {
          return <Text type="success"><ArrowDownOutlined /> ¥{Math.abs(diff).toFixed(2)}</Text>;
        }
        return <Text>¥{diff.toFixed(2)}</Text>;
      },
    },
     {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        let color = 'default';
        let text = '正常';
        if (status === 'adjustment_needed') {
          color = 'warning';
          text = '需调价';
        } else if (status === 'adjusted') {
          color = 'success';
          text = '已调价';
        } else if (status === 'normal') {
          color = 'success';
          text = '正常';
        }
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        record.status === 'adjustment_needed' ? (
          <Button type="primary" size="small" onClick={() => handlePriceAdjusted(record)}>
            已调价
          </Button>
        ) : null
      ),
    },
  ];

  const keyProductColumns = [
    { title: '排名', dataIndex: 'key', key: 'key', render: text => <strong>{text}</strong> },
    { title: '产品名称', dataIndex: 'name', key: 'name' },
    { title: '销售额', dataIndex: 'sales', key: 'sales' },
    { title: '销售占比', dataIndex: 'proportion', key: 'proportion' },
    { title: '操作', key: 'action', render: () => <Button type="link" size="small">分析</Button> },
  ];

  const priceMonitoringColumns = [
    { title: '产品名称', dataIndex: 'product_name', key: 'product_name' },
    {
      title: '平均价格',
      dataIndex: 'avg_price',
      key: 'avg_price',
      render: (text) => `¥${parseFloat(text).toFixed(2)}`
    },
    {
      title: '价格区间',
      key: 'price_range',
      render: (_, record) => `¥${parseFloat(record.min_price).toFixed(2)} - ¥${parseFloat(record.max_price).toFixed(2)}`
    },
    {
      title: '波动率',
      dataIndex: 'price_volatility',
      key: 'price_volatility',
      render: (text) => {
        const volatility = parseFloat(text);
        const color = volatility > 10 ? '#ff4d4f' : volatility > 5 ? '#faad14' : '#52c41a';
        return <span style={{ color }}>{volatility.toFixed(1)}%</span>;
      },
      sorter: (a, b) => a.price_volatility - b.price_volatility,
    },
    { title: '到货次数', dataIndex: 'arrival_count', key: 'arrival_count' },
    { title: '日期', dataIndex: 'arrival_date', key: 'arrival_date' },
  ];

  const handleAIAnalysis = (title) => {
    message.info(`正在对 ${title} 进行AI分析...`);
  };

  const renderCardTitle = (title, tooltip) => (
    <Space>
      {title}
      <Tooltip title={tooltip}>
        <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
      </Tooltip>
    </Space>
  );

  const renderAIButton = (title) => (
    <Button
      icon={<RobotOutlined />}
      type="primary"
      ghost
      onClick={() => handleAIAnalysis(title)}
    >
      智能分析
    </Button>
  );

  const handleFetchIndustryNews = async () => {
    setNewsLoading(true);
    try {
        const projectId = localStorage.getItem('project_id');
        if (!projectId) {
            message.error("项目ID不存在");
            return;
        }

        // 获取默认AI助手或使用第一个可用的助手
        const assistantsRes = await apiService.project.ai.getAssistants(projectId);
        if (!assistantsRes.success || !assistantsRes.data || assistantsRes.data.length === 0) {
            message.error("未找到可用的AI助手");
            return;
        }

        const defaultAssistant = assistantsRes.data[0]; // 使用第一个助手

        // 构建获取行业新闻的提示词
        const prompt = `
请帮我获取今日零售行业的最新资讯和动态，包括：
1. 行业政策变化
2. 市场趋势分析
3. 新技术应用
4. 竞争对手动态
5. 消费者行为变化

请提供3-5条最重要的行业新闻，每条新闻包含标题和简要摘要。
请确保信息来源可靠，内容与零售行业相关。

今日日期：${new Date().toLocaleDateString('zh-CN')}
        `;

        // 调用AI助手获取行业新闻
        const response = await apiService.project.ai.chatWithAssistant(projectId, {
            assistant_id: defaultAssistant.id,
            message: prompt,
            context: {
                type: 'industry_news',
                date: new Date().toISOString().split('T')[0],
                industry: 'retail'
            }
        });

        if (response.success) {
            const newsContent = response.data?.message || response.message || '';

            // 解析AI返回的新闻内容
            const newsItems = parseNewsContent(newsContent);

            // 更新行业动态，保留AI建议
            const existingAdvice = industryNews.filter(item => item.type === 'ai_advice');
            setIndustryNews([...existingAdvice, ...newsItems]);

            message.success("行业动态已更新");
        } else {
            message.error("获取行业动态失败");
        }
    } catch (error) {
        console.error("获取行业动态时出错:", error);
        message.error("获取行业动态时出错");
    } finally {
        setNewsLoading(false);
    }
  };

  // 解析AI返回的新闻内容
  const parseNewsContent = (content) => {
    const newsItems = [];
    const lines = content.split('\n').filter(line => line.trim());

    let currentTitle = '';
    let currentSummary = '';

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();

        // 检测标题（通常以数字开头或包含特定格式）
        if (trimmedLine.match(/^\d+\./) || trimmedLine.includes('：') || trimmedLine.includes(':')) {
            if (currentTitle) {
                // 保存前一条新闻
                newsItems.push({
                    id: Date.now() + index,
                    title: currentTitle,
                    summary: currentSummary,
                    type: 'industry_news',
                    timestamp: new Date().toISOString()
                });
            }
            currentTitle = trimmedLine.replace(/^\d+\./, '').trim();
            currentSummary = '';
        } else if (trimmedLine && currentTitle) {
            // 累积摘要内容
            currentSummary += (currentSummary ? ' ' : '') + trimmedLine;
        }
    });

    // 添加最后一条新闻
    if (currentTitle) {
        newsItems.push({
            id: Date.now() + lines.length,
            title: currentTitle,
            summary: currentSummary,
            type: 'industry_news',
            timestamp: new Date().toISOString()
        });
    }

    // 如果解析失败，创建一个包含完整内容的新闻项
    if (newsItems.length === 0 && content.trim()) {
        newsItems.push({
            id: Date.now(),
            title: '今日行业动态',
            summary: content.substring(0, 200) + (content.length > 200 ? '...' : ''),
            type: 'industry_news',
            timestamp: new Date().toISOString()
        });
    }

    return newsItems.slice(0, 5); // 最多返回5条新闻
  };

  const handleFetchNews = async () => {
    setNewsLoading(true);
    try {
        const projectId = localStorage.getItem('project_id');
        if (!projectId) {
            message.error("无效的项目ID格式: undefined");
            return;
        }

        const response = await apiService.project.ai.getAssistants(projectId);
        if (response.success) {
            setAssistants(response.data);
            setIsAssistantModalVisible(true);
        } else {
            message.error("获取AI助手列表失败");
        }
    } catch (error) {
        console.error("获取AI助手列表时出错:", error);
        message.error("获取AI助手列表时出错");
    } finally {
        setNewsLoading(false);
    }
  };

  const onAssistantSelect = async (assistantId) => {
    setIsAssistantModalVisible(false);
    setNewsLoading(true);
    try {
        const projectId = localStorage.getItem('project_id');
        if (!projectId) {
            message.error("项目ID不存在");
            return;
        }

        // 组装门店关键数据
        const storeData = {
            storeId: selectedStore,
            date: selectedDate.format('YYYY-MM-DD'),
            kpiData: {
                totalRevenue: kpiData.totalRevenue,
                totalLoss: kpiData.totalLoss,
                customerTraffic: kpiData.customerTraffic,
                salesTarget: kpiData.salesTarget
            },
            weather: weatherData,
            keyProducts: keyProductData.slice(0, 5), // 取前5个重点产品
            pricePower: pricePowerData.slice(0, 3), // 取前3个价格力产品
            lossRank: lossRankData.slice(0, 3) // 取前3个损耗产品
        };

        // 构建AI提示词
        const prompt = `
作为门店经营顾问，请基于以下今日门店数据为我提供经营建议：

门店基本信息：
- 门店ID: ${storeData.storeId}
- 分析日期: ${storeData.date}

今日关键指标：
- 总销售额: ¥${storeData.kpiData.totalRevenue.value || 0}（较昨日${storeData.kpiData.totalRevenue.yoy > 0 ? '上升' : '下降'}${Math.abs(storeData.kpiData.totalRevenue.yoy || 0)}%）
- 客流量: ${storeData.kpiData.customerTraffic.value || 0}人（较昨日${storeData.kpiData.customerTraffic.yoy > 0 ? '增加' : '减少'}${Math.abs(storeData.kpiData.customerTraffic.yoy || 0)}%）
- 损耗率: ${storeData.kpiData.totalLoss.value || 0}%
- 销售目标完成率: ${Math.round((storeData.kpiData.salesTarget.completed / storeData.kpiData.salesTarget.total) * 100)}%

天气情况：
${storeData.weather ? `${storeData.weather.city} ${storeData.weather.current?.description || storeData.weather.description} ${storeData.weather.current?.temperature || storeData.weather.temperature}°C` : '天气数据暂无'}

重点产品表现：
${storeData.keyProducts.map((product, index) => `${index + 1}. ${product.name}: 销售额 ${product.sales || 'N/A'}`).join('\n')}

价格竞争力：
${storeData.pricePower.map((product, index) => `${index + 1}. ${product.name}: 我方价格 ¥${product.our_price || 0}, 市场均价 ¥${product.market_price || 0}`).join('\n')}

损耗情况：
${storeData.lossRank.map((product, index) => `${index + 1}. ${product.name}: 损耗 ${product.loss || 'N/A'}`).join('\n')}

请提供：
1. 今日经营状况分析
2. 具体改进建议
3. 明日经营重点
4. 风险提醒

请用简洁明了的语言，重点突出可执行的建议。
        `;

        // 调用AI助手
        const response = await apiService.project.ai.chatWithAssistant(projectId, {
            assistant_id: assistantId,
            message: prompt,
            context: {
                type: 'store_daily_advice',
                store_id: selectedStore,
                date: storeData.date,
                data: storeData
            }
        });

        if (response.success) {
            const aiAdvice = response.data?.message || response.message || '获取建议成功';
            setIndustryNews([{
                id: Date.now(),
                title: '今日经营建议',
                content: aiAdvice,
                type: 'ai_advice',
                timestamp: new Date().toISOString()
            }]);
            message.success("AI建议已生成");
        } else {
            message.error("获取AI建议失败");
        }
    } catch (error) {
        console.error("获取AI建议时出错:", error);
        message.error("获取AI建议时出错");
    } finally {
        setNewsLoading(false);
    }
  };

  const renderStatisticCard = (title, data, icon) => {
    if (!data) {
      return <Skeleton active />;
    }
    return (
      <Card>
        <Statistic
          title={title}
          value={data.value}
          precision={title.includes('率') ? 1 : 0}
          valueStyle={{ color: '#3f8600' }}
          prefix={icon}
          suffix={title.includes('率') ? '%' : ''}
        />
        <div style={{ marginTop: '10px', fontSize: '14px' }}>
          <span>
            同比 <Text type={data.yoy?.startsWith('+') ? 'danger' : 'success'}>{data.yoy} <ArrowUpOutlined /></Text>
          </span>
          <span style={{ marginLeft: '20px' }}>
            环比 <Text type={data.mom?.startsWith('+') ? 'danger' : 'success'}>{data.mom} <ArrowDownOutlined /></Text>
          </span>
        </div>
      </Card>
    );
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ backgroundColor: '#fff', padding: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <div>
            <Title level={3} style={{ margin: 0 }}>门店大盘</Title>
            <Paragraph type="secondary" style={{ margin: 0 }}>实时监控门店核心业务指标，辅助经营决策。</Paragraph>
          </div>
          <Space>
            <Select
              style={{ width: 200 }}
              placeholder="选择门店"
              value={selectedStore}
              onChange={setSelectedStore}
              loading={stores.length === 0}
            >
              {stores.map(store => <Option key={store.id} value={store.id}>{store.name}</Option>)}
            </Select>
            <DatePicker value={selectedDate} onChange={setSelectedDate} />
            <Button type="primary" onClick={fetchAllDashboardData} loading={loading}>查询</Button>
          </Space>
        </div>
        
        <Spin spinning={loading}>
          {/* KPI Cards */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title={<Space><DollarCircleOutlined />总销售额</Space>}
                  value={kpiData.totalRevenue.value || 0}
                  valueStyle={{ color: '#cf1322' }}
                  suffix={<span style={{fontSize: 12, color: '#aaa'}}>元</span>}
                />
                <div style={{fontSize: 12, color: '#888'}}>
                  较昨日: {renderComparison(kpiData.totalRevenue.yoy, '')} | 
                  较上周: {renderComparison(kpiData.totalRevenue.mom, '')}
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title={<Space><AlertOutlined />总损耗率</Space>}
                  value={kpiData.totalLoss.value || 0}
                  valueStyle={{ color: '#3f8600' }}
                  suffix={<span style={{fontSize: 12, color: '#aaa'}}>%</span>}
                />
                <div style={{fontSize: 12, color: '#888'}}>
                  较昨日: {renderComparison(kpiData.totalLoss.yoy, '')} | 
                  较上周: {renderComparison(kpiData.totalLoss.mom, '')}
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title={<Space><UserOutlined />客流量</Space>}
                  value={kpiData.customerTraffic.value || 0}
                  valueStyle={{ color: '#1890ff' }}
                  suffix={<span style={{fontSize: 12, color: '#aaa'}}>人</span>}
                />
                <div style={{fontSize: 12, color: '#888'}}>
                  较昨日: {renderComparison(kpiData.customerTraffic.yoy, '')} | 
                  较上周: {renderComparison(kpiData.customerTraffic.mom, '')}
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <div style={{textAlign: 'center'}}>
                    <Text>销售目标</Text>
                    <Progress 
                        type="dashboard"
                        percent={Math.round((kpiData.salesTarget.completed / kpiData.salesTarget.total) * 100)}
                        format={percent => `${percent}%`}
                    />
                    <Text type="secondary" style={{fontSize: 12}}>
                        {kpiData.salesTarget.completed} / {kpiData.salesTarget.total}
                    </Text>
                </div>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
            {/* Left Column */}
            <Col xs={24} md={16}>
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                <Card
                  title="价格监控"
                  extra={
                    <Space>
                      <Select
                        value={priceMonitoringPeriod}
                        onChange={setPriceMonitoringPeriod}
                        size="small"
                        style={{ width: 80 }}
                      >
                        <Option value="day">日</Option>
                        <Option value="week">周</Option>
                        <Option value="month">月</Option>
                      </Select>
                      <Button
                        size="small"
                        onClick={() => navigate(`/project/operation/price-monitoring-detail?storeId=${selectedStore}`)}
                      >
                        查看详情
                      </Button>
                      <Button icon={<RobotOutlined />} size="small" type="primary" ghost onClick={() => handleAIAnalysis('价格监控')}>AI分析</Button>
                    </Space>
                  }
                >
                  {priceMonitoringData.price_trends && priceMonitoringData.price_trends.length > 0 ? (
                    <>
                      <div style={{ marginBottom: 16 }}>
                        <Space>
                          <Text>平均波动率: <strong style={{ color: priceMonitoringData.summary?.avg_volatility > 10 ? '#ff4d4f' : '#52c41a' }}>{priceMonitoringData.summary?.avg_volatility}%</strong></Text>
                          <Text>高波动产品: <strong style={{ color: '#faad14' }}>{priceMonitoringData.summary?.high_volatility_count}</strong></Text>
                          <Text>监控产品: <strong>{priceMonitoringData.summary?.total_products}</strong></Text>
                        </Space>
                      </div>
                      <Table
                        dataSource={priceMonitoringData.price_trends}
                        columns={priceMonitoringColumns}
                        pagination={{ pageSize: 5 }}
                        size="small"
                        rowKey={(record) => `${record.product_name}-${record.arrival_date}`}
                      />
                    </>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <Text type="secondary">暂无价格监控数据</Text>
                    </div>
                  )}
                </Card>
                <Card
                  title="价格力分析"
                  extra={<Button icon={<RobotOutlined />} size="small" type="primary" ghost onClick={() => handleAIAnalysis('价格力分析')}>AI分析</Button>}
                >
                  {pricePowerData.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <Text type="secondary">暂无价格力数据</Text>
                    </div>
                  ) : (
                    <Table
                      dataSource={pricePowerData}
                      columns={pricePowerColumns}
                      pagination={false}
                      size="small"
                      rowKey={(record) => record.id || `price-${Math.random()}`}
                      loading={loading}
                    />
                  )}
                </Card>
                <Card 
                  title="损耗排行 (按产品)"
                  extra={<Button icon={<RobotOutlined />} size="small" type="primary" ghost onClick={() => handleAIAnalysis('损耗排行')}>AI分析</Button>}
                >
                  <Table dataSource={lossRankData} columns={lossRankColumns} pagination={false} size="small" rowKey="id"/>
                </Card>
                <Card
                  title="重点单品分析"
                  extra={<Button icon={<RobotOutlined />} size="small" type="primary" ghost onClick={() => handleAIAnalysis('重点单品分析')}>AI分析</Button>}
                >
                  <Table dataSource={keyProductData} columns={keyProductColumns} pagination={false} size="small" rowKey="id"/>
                </Card>
              </Space>
            </Col>
            {/* Right Column */}
            <Col xs={24} md={8}>
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                 <Card title="决策辅助">
                  {weatherData ? (
                    <div style={{ marginBottom: 16 }}>
                      <Title level={5}><CloudOutlined /> {weatherData.city} 天气</Title>
                      {/* 今日天气 */}
                      <div style={{ marginBottom: 12, padding: 12, backgroundColor: '#f0f9ff', borderRadius: 6 }}>
                        <Text strong>今日: </Text>
                        <Text>{`${weatherData.current?.temperature || weatherData.temperature}°C, ${weatherData.current?.description || weatherData.description}`}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {weatherData.current?.wind_power || weatherData.wind_power} | {weatherData.current?.humidity || weatherData.humidity}
                        </Text>
                      </div>
                      {/* 未来3天预报 */}
                      {weatherData.forecasts && weatherData.forecasts.length > 0 && (
                        <div>
                          <Text strong style={{ fontSize: 12, color: '#666' }}>未来3天预报:</Text>
                          {weatherData.forecasts.map((forecast, index) => (
                            <div key={index} style={{
                              marginTop: 8,
                              padding: 8,
                              backgroundColor: '#fafafa',
                              borderRadius: 4,
                              fontSize: 12
                            }}>
                              <Text strong>{forecast.date}: </Text>
                              <Text>{forecast.day_weather} {forecast.night_temp}°C~{forecast.day_temp}°C</Text>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : <Text type="secondary">天气信息加载中...</Text>}
                  <Divider />
                  <div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                      <Title level={5} style={{ margin: 0 }}><ReadOutlined /> 行业动态</Title>
                      <Button
                        size="small"
                        type="primary"
                        ghost
                        onClick={handleFetchIndustryNews}
                        loading={newsLoading}
                      >
                        获取新闻
                      </Button>
                    </div>
                    {industryNews.filter(item => item.type !== 'ai_advice').length > 0 ? (
                      <List
                        itemLayout="horizontal"
                        dataSource={industryNews.filter(item => item.type !== 'ai_advice')}
                        renderItem={item => (
                          <List.Item>
                            <List.Item.Meta
                              avatar={<ReadOutlined />}
                              title={<a href="#!" style={{ fontSize: 14 }}>{item.title}</a>}
                              description={item.summary && (
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                  {item.summary.substring(0, 100)}...
                                </Text>
                              )}
                            />
                          </List.Item>
                        )}
                      />
                    ) : (
                      <div style={{ textAlign: 'center', padding: '20px' }}>
                        <ReadOutlined style={{ fontSize: 32, color: '#d9d9d9', marginBottom: 8 }} />
                        <div>
                          <Text type="secondary">暂无行业动态</Text><br/>
                          <Text type="secondary" style={{ fontSize: 12 }}>点击"获取新闻"获取最新资讯</Text>
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
                <Card
                  title="AI 智能助理"
                  extra={<Button size="small" type="primary" onClick={handleFetchNews} loading={newsLoading}>获取建议</Button>}
                >
                  {industryNews.length > 0 ? (
                    industryNews.map((item, index) => (
                      <div key={index} style={{ marginBottom: 16 }}>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                          <RobotOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                          <Text strong>{item.title}</Text>
                        </div>
                        <div style={{
                          padding: 12,
                          backgroundColor: '#f6ffed',
                          border: '1px solid #b7eb8f',
                          borderRadius: 6,
                          whiteSpace: 'pre-wrap',
                          fontSize: 14,
                          lineHeight: 1.6
                        }}>
                          {item.content}
                        </div>
                        {item.timestamp && (
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            生成时间: {new Date(item.timestamp).toLocaleString()}
                          </Text>
                        )}
                      </div>
                    ))
                  ) : (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <RobotOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                      <div>
                        <Text type="secondary">点击"获取建议"按钮</Text><br/>
                        <Text type="secondary" style={{ fontSize: 12 }}>AI将基于今日经营数据为您提供专业建议</Text>
                      </div>
                    </div>
                  )}
                </Card>
              </Space>
            </Col>
          </Row>
        </Spin>
      </div>

      <Modal
        title="选择AI助手获取行业动态"
        open={isAssistantModalVisible}
        onCancel={() => setIsAssistantModalVisible(false)}
        footer={null}
      >
        <List
          dataSource={assistants}
          renderItem={assistant => (
            <List.Item
              actions={[<Button onClick={() => onAssistantSelect(assistant.id)}>选择</Button>]}
            >
              <List.Item.Meta
                title={assistant.name}
                description={assistant.description}
              />
            </List.Item>
          )}
        />
      </Modal>
    </div>
  );
};

export default StoreSuperDashboard; 