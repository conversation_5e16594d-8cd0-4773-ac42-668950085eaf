import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  DatePicker,
  Select,
  Input,
  Tag,
  message,
  Typography,
  Row,
  Col,
  Tabs,
  Statistic,
  Popconfirm,
  Empty
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  Pie<PERSON>hartOutlined,
  Bar<PERSON><PERSON>Outlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { Column, Pie } from '@ant-design/plots';
import dateTimeUtils from '../../../utils/dateTimeUtils';
import SalesMonthlyReport from './SalesMonthlyReport';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 销售上报组件
 * 用于查看和管理门店销售上报，包括渠道销售和充值/售卡
 */
const SalesReport = () => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState([]);
  const [stores, setStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(7, 'days'), dayjs()]);
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('1');
  const [reportType, setReportType] = useState('all');
  const [statistics, setStatistics] = useState({
    total_sales: 0,
    total_orders: 0,
    total_customers: 0,
    average_order_value: 0,
    channel_sales: {},
    recharge_sales: {}
  });
  const [channelData, setChannelData] = useState([]);
  const [rechargeData, setRechargeData] = useState([]);

  const navigate = useNavigate();

  // 报告类型文本映射
  const reportTypeTexts = {
    daily: '日报',
    weekly: '周报',
    monthly: '月报',
    recharge: '充值/售卡'
  };

  // 状态文本映射
  const statusTexts = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝'
  };

  // 状态颜色映射
  const statusColors = {
    draft: 'default',
    submitted: 'processing',
    approved: 'success',
    rejected: 'error'
  };

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取销售上报列表
  const fetchReports = async () => {
    setLoading(true);
    try {
      const params = {
        store_id: selectedStore,
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        report_type: reportType === 'all' ? undefined : reportType,
        search: searchText || undefined
      };

      const response = await apiService.project.salesReport.getList(params);
      if (response && response.items) {
        setReports(response.items);
        calculateStatistics(response.items);
      } else {
        setReports([]);
        resetStatistics();
      }
    } catch (error) {
      console.error('获取销售上报列表失败:', error);
      message.error('获取销售上报列表失败');
      setReports([]);
      resetStatistics();
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    // 初始化日期时间工具
    dateTimeUtils.initDateTimeUtils();
    fetchStores();
  }, []);

  // 当筛选条件变化时，重新获取数据
  useEffect(() => {
    if (dateRange && dateRange.length === 2) {
      fetchReports();
    }
  }, [selectedStore, dateRange, reportType, searchText]);

  // 重置统计数据
  const resetStatistics = () => {
    setStatistics({
      total_sales: 0,
      total_orders: 0,
      total_customers: 0,
      average_order_value: 0,
      channel_sales: {},
      recharge_sales: {},
      payment_methods: {},
      today_sales: 0,
      today_orders: 0,
      today_customers: 0,
      recharge_amount: 0,
      card_sales_amount: 0
    });
    setChannelData([]);
    setRechargeData([]);
  };

  // 计算统计数据
  const calculateStatistics = (reportList) => {
    if (!reportList || reportList.length === 0) {
      resetStatistics();
      return;
    }

    let totalSales = 0;
    let totalOrders = 0;
    let totalCustomers = 0;
    const channelSales = {};
    const rechargeSales = {};
    const paymentMethods = {};

    // 用于处理班次报表的日期累加计算
    const dailyReports = {};

    // 第一步：按日期分组，特别处理班次报表
    reportList.forEach(report => {
      // 如果是班次报表，需要按日期累加
      if (report.report_type === 'shift') {
        const reportDate = dayjs(report.report_date).format('YYYY-MM-DD');

        if (!dailyReports[reportDate]) {
          dailyReports[reportDate] = {
            total_sales: 0,
            online_sales: 0,
            offline_sales: 0,
            total_orders: 0,
            total_customers: 0,
            recharge_amount: 0,
            card_sales_amount: 0,
            channel_sales: {},
            payment_methods: {}
          };
        }

        // 累加销售额
        dailyReports[reportDate].total_sales += parseFloat(report.total_sales || 0);
        dailyReports[reportDate].online_sales += parseFloat(report.online_sales || 0);
        dailyReports[reportDate].offline_sales += parseFloat(report.offline_sales || 0);

        // 累加订单数和客流量
        dailyReports[reportDate].total_orders += parseInt(report.total_orders || 0);
        dailyReports[reportDate].total_customers += parseInt(report.total_customers || 0);

        // 累加充值/售卡金额
        dailyReports[reportDate].recharge_amount += parseFloat(report.recharge_amount || 0);
        dailyReports[reportDate].card_sales_amount += parseFloat(report.card_sales_amount || 0);

        // 处理渠道销售数据
        if (report.channel_sales) {
          if (typeof report.channel_sales === 'object' && !Array.isArray(report.channel_sales)) {
            // 对象格式
            Object.entries(report.channel_sales).forEach(([channelId, amount]) => {
              // 尝试从渠道列表中获取渠道名称
              const channel = stores.find(store => store.id === channelId);
              const channelName = channel ? channel.name : (channelId || '未知渠道');
              dailyReports[reportDate].channel_sales[channelName] =
                (dailyReports[reportDate].channel_sales[channelName] || 0) + parseFloat(amount || 0);
            });
          } else if (Array.isArray(report.channel_sales)) {
            // 数组格式
            report.channel_sales.forEach(channel => {
              const channelId = channel.channel_id || channel.id;
              if (channelId) {
                // 尝试从渠道列表中获取渠道名称
                const storeChannel = stores.find(store => store.id === channelId);
                const channelName = channel.channel_name || (storeChannel ? storeChannel.name : (channelId || '未知渠道'));
                dailyReports[reportDate].channel_sales[channelName] =
                  (dailyReports[reportDate].channel_sales[channelName] || 0) + parseFloat(channel.amount || 0);
              }
            });
          }
        }

        // 处理支付方式数据
        if (report.payment_methods) {
          if (typeof report.payment_methods === 'object' && !Array.isArray(report.payment_methods)) {
            // 对象格式
            Object.entries(report.payment_methods).forEach(([methodId, amount]) => {
              // 使用支付方式名称作为键
              const methodName = methodId || '未知支付方式';
              dailyReports[reportDate].payment_methods[methodName] =
                (dailyReports[reportDate].payment_methods[methodName] || 0) + parseFloat(amount || 0);
            });
          } else if (Array.isArray(report.payment_methods)) {
            // 数组格式
            report.payment_methods.forEach(payment => {
              const methodId = payment.method || payment.method_id;
              if (methodId) {
                // 使用支付方式名称作为键
                const methodName = payment.method_name || methodId || '未知支付方式';
                dailyReports[reportDate].payment_methods[methodName] =
                  (dailyReports[reportDate].payment_methods[methodName] || 0) + parseFloat(payment.amount || 0);
              }
            });
          }
        }
      }
    });

    // 第二步：处理所有报告（非班次报表直接处理，班次报表使用累加后的数据）
    reportList.forEach(report => {
      // 如果是班次报表，跳过，因为已经在上面处理过了
      if (report.report_type === 'shift') {
        return;
      }

      // 累加总销售额
      totalSales += parseFloat(report.total_sales || 0);

      // 累加总订单数和客流量
      totalOrders += parseInt(report.total_orders || 0);
      totalCustomers += parseInt(report.total_customers || 0);

      // 处理渠道销售数据
      if (report.channel_sales) {
        if (typeof report.channel_sales === 'object' && !Array.isArray(report.channel_sales)) {
          // 对象格式
          Object.entries(report.channel_sales).forEach(([channelId, amount]) => {
            // 尝试从渠道列表中获取渠道名称
            const channel = stores.find(store => store.id === channelId);
            const channelName = channel ? channel.name : (channelId || '未知渠道');
            channelSales[channelName] = (channelSales[channelName] || 0) + parseFloat(amount || 0);
          });
        } else if (Array.isArray(report.channel_sales)) {
          // 数组格式
          report.channel_sales.forEach(channel => {
            const channelName = channel.channel_name || '未知渠道';
            channelSales[channelName] = (channelSales[channelName] || 0) + parseFloat(channel.amount || 0);
          });
        }
      }

      // 处理充值/售卡数据
      const rechargeAmount = parseFloat(report.recharge_amount || 0);
      const cardSalesAmount = parseFloat(report.card_sales_amount || 0);

      if (rechargeAmount > 0) {
        rechargeSales['充值'] = (rechargeSales['充值'] || 0) + rechargeAmount;
      }

      if (cardSalesAmount > 0) {
        rechargeSales['售卡'] = (rechargeSales['售卡'] || 0) + cardSalesAmount;
      }

      // 处理支付方式数据
      if (report.payment_methods) {
        if (typeof report.payment_methods === 'object' && !Array.isArray(report.payment_methods)) {
          // 对象格式
          Object.entries(report.payment_methods).forEach(([methodId, amount]) => {
            // 尝试获取支付方式名称
            const methodName = methodId || '未知支付方式';
            paymentMethods[methodName] = (paymentMethods[methodName] || 0) + parseFloat(amount || 0);
          });
        } else if (Array.isArray(report.payment_methods)) {
          // 数组格式
          report.payment_methods.forEach(payment => {
            const methodName = payment.method_name || payment.method || '未知支付方式';
            paymentMethods[methodName] = (paymentMethods[methodName] || 0) + parseFloat(payment.amount || 0);
          });
        }
      }
    });

    // 第三步：将班次报表的累加数据加入总统计
    Object.values(dailyReports).forEach(dailyReport => {
      // 累加总销售额
      totalSales += dailyReport.total_sales;

      // 累加总订单数和客流量
      totalOrders += dailyReport.total_orders;
      totalCustomers += dailyReport.total_customers;

      // 处理渠道销售数据
      Object.entries(dailyReport.channel_sales).forEach(([channelId, amount]) => {
        // 尝试从渠道列表中获取渠道名称
        const channel = stores.find(store => store.id === channelId);
        const channelName = channel ? channel.name : (channelId || '未知渠道');
        channelSales[channelName] = (channelSales[channelName] || 0) + amount;
      });

      // 处理充值/售卡数据
      if (dailyReport.recharge_amount > 0) {
        rechargeSales['充值'] = (rechargeSales['充值'] || 0) + dailyReport.recharge_amount;
      }

      if (dailyReport.card_sales_amount > 0) {
        rechargeSales['售卡'] = (rechargeSales['售卡'] || 0) + dailyReport.card_sales_amount;
      }

      // 处理支付方式数据
      Object.entries(dailyReport.payment_methods).forEach(([methodId, amount]) => {
        // 尝试获取支付方式名称
        const methodName = methodId || '未知支付方式';
        paymentMethods[methodName] = (paymentMethods[methodName] || 0) + amount;
      });
    });

    // 计算平均订单金额
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

    // 计算今日销售数据
    const today = dayjs().format('YYYY-MM-DD');
    let todaySales = 0;
    let todayOrders = 0;
    let todayCustomers = 0;

    // 从报告列表中筛选出今日的报告
    reportList.forEach(report => {
      const reportDate = dayjs(report.report_date).format('YYYY-MM-DD');
      if (reportDate === today) {
        // 如果是班次报表，使用累加后的数据
        if (report.report_type === 'shift') {
          // 班次报表的数据已经在 dailyReports 中累加
          return;
        }

        // 累加今日销售额、订单数和客流量
        todaySales += parseFloat(report.total_sales || 0);
        todayOrders += parseInt(report.total_orders || 0);
        todayCustomers += parseInt(report.total_customers || 0);
      }
    });

    // 如果有今日的班次报表累加数据，加入今日统计
    if (dailyReports[today]) {
      todaySales += dailyReports[today].total_sales;
      todayOrders += dailyReports[today].total_orders;
      todayCustomers += dailyReports[today].total_customers;
    }

    // 更新统计数据
    setStatistics({
      total_sales: totalSales,
      total_orders: totalOrders,
      total_customers: totalCustomers,
      average_order_value: averageOrderValue,
      channel_sales: channelSales,
      recharge_sales: rechargeSales,
      payment_methods: paymentMethods,
      today_sales: todaySales,
      today_orders: todayOrders,
      today_customers: todayCustomers,
      recharge_amount: rechargeSales['充值'] || 0,
      card_sales_amount: rechargeSales['售卡'] || 0
    });

    // 准备图表数据
    const channelChartData = Object.entries(channelSales)
      .filter(([_, value]) => parseFloat(value) > 0)
      .map(([name, value]) => ({
        name,
        value: parseFloat(value)
      }))
      .sort((a, b) => b.value - a.value);
    setChannelData(channelChartData);

    const rechargeChartData = Object.entries(rechargeSales)
      .filter(([_, value]) => parseFloat(value) > 0)
      .map(([name, value]) => ({
        name,
        value: parseFloat(value)
      }))
      .sort((a, b) => b.value - a.value);
    setRechargeData(rechargeChartData);
  };

  // 处理删除报告
  const handleDelete = async (id) => {
    try {
      await apiService.project.salesReport.delete(id);
      // 删除成功，204状态码不会返回内容
      message.success('销售上报删除成功');
      fetchReports(); // 刷新列表
    } catch (error) {
      console.error('删除销售上报失败:', error);
      message.error('删除销售上报失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  // 处理门店选择变化
  const handleStoreChange = (value) => {
    setSelectedStore(value);
  };

  // 处理报告类型变化
  const handleReportTypeChange = (value) => {
    setReportType(value);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 导出报告
  const handleExport = async () => {
    try {
      const params = {
        store_id: selectedStore,
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        report_type: reportType === 'all' ? undefined : reportType
      };

      const response = await apiService.project.salesReport.export(params);
      if (response) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `销售报告_${dayjs().format('YYYY-MM-DD')}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        message.success('导出成功');
      }
    } catch (error) {
      console.error('导出销售上报失败:', error);
      message.error('导出销售上报失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      width: '15%',
      render: (text) => <span>{text || '未知门店'}</span>
    },
    {
      title: '报告日期',
      dataIndex: 'report_date',
      key: 'report_date',
      width: '12%',
      sorter: (a, b) => new Date(a.report_date) - new Date(b.report_date),
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '报告类型',
      dataIndex: 'report_type',
      key: 'report_type',
      width: '10%',
      render: (text) => reportTypeTexts[text] || text
    },
    {
      title: '销售总额',
      dataIndex: 'total_sales',
      key: 'total_sales',
      width: '12%',
      sorter: (a, b) => a.total_sales - b.total_sales,
      render: (text) => `¥${(text || 0).toFixed(2)}`
    },
    {
      title: '订单数',
      dataIndex: 'total_orders',
      key: 'total_orders',
      width: '10%',
      render: (text) => `${text || 0}单`
    },
    {
      title: '充值/售卡',
      dataIndex: 'recharge_amount',
      key: 'recharge_amount',
      width: '12%',
      render: (text, record) => {
        const rechargeAmount = record.recharge_amount || 0;
        const cardSalesAmount = record.card_sales_amount || 0;
        return `¥${(rechargeAmount + cardSalesAmount).toFixed(2)}`;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: (text) => (
        <Tag color={statusColors[text] || 'default'}>
          {statusTexts[text] || text}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: '12%',
      render: (text) => text ? dateTimeUtils.formatDateTime(text) : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: '15%',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/project/store/sales-report/detail/${record.id}`)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => navigate(`/project/store/sales-report/edit/${record.id}`)}
            disabled={record.status === 'approved'}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此销售上报吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={record.status === 'approved'}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 渲染统计卡片
  const renderStatisticsCards = () => {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总销售额"
              value={statistics.total_sales}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={statistics.total_orders}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总客流量"
              value={statistics.total_customers}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均订单金额"
              value={statistics.average_order_value}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染渠道销售图表
  const renderChannelSalesChart = () => {
    // 确保数据有效
    if (!channelData || channelData.length === 0) {
      return (
        <Card title={<Space><BarChartOutlined /> 渠道销售分布</Space>} style={{ marginTop: 16 }}>
          <Empty description="暂无渠道销售数据" />
        </Card>
      );
    }

    // 柱状图配置
    const columnConfig = {
      data: channelData,
      xField: 'name',
      yField: 'value',
      seriesField: 'name',
      label: {
        position: 'top',
        style: {
          fontWeight: 'bold',
        },
        formatter: (datum) => `¥${datum.value.toFixed(2)}`,
      },
      color: ['#1890ff', '#52c41a', '#faad14', '#722ed1', '#eb2f96', '#fa541c'],
      meta: {
        name: { alias: '渠道' },
        value: { alias: '销售额' },
      },
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `¥${datum.value.toFixed(2)}` };
        },
      },
      xAxis: {
        label: {
          autoRotate: true,
          autoHide: false,
          autoEllipsis: true,
        },
      },
      yAxis: {
        label: {
          formatter: (v) => `¥${v}`,
        },
      },
      interactions: [{ type: 'element-active' }],
    };

    return (
      <Card title={<Space><BarChartOutlined /> 渠道销售分布</Space>} style={{ marginTop: 16 }}>
        <Column {...columnConfig} height={300} />
      </Card>
    );
  };

  // 渲染充值/售卡图表
  const renderRechargeSalesChart = () => {
    // 确保数据有效
    if (!rechargeData || rechargeData.length === 0) {
      return (
        <Card title={<Space><PieChartOutlined /> 充值/售卡分布</Space>} style={{ marginTop: 16 }}>
          <Empty description="暂无充值/售卡数据" />
        </Card>
      );
    }

    // 饼图配置
    const pieConfig = {
      data: rechargeData,
      angleField: 'value',
      colorField: 'name',
      nameField: 'name',
      radius: 0.8,
      label: {
        type: 'outer',
        formatter: (datum) => `${datum.name}: ${(datum.percent * 100).toFixed(2)}%`
      },
      legend: {
        position: 'bottom',
        layout: 'horizontal',
      },
      interactions: [{ type: 'pie-legend-active' }, { type: 'element-active' }],
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `¥${datum.value.toFixed(2)}` };
        },
      },
      statistic: {
        title: {
          style: { fontSize: '14px', color: '#4B535E' },
          content: '充值/售卡总额',
        },
        content: {
          style: { fontSize: '20px', color: '#1890ff', fontWeight: 'bold' },
          formatter: () => {
            const total = rechargeData.reduce((sum, item) => sum + item.value, 0);
            return `¥${total.toFixed(2)}`;
          },
        },
      },
    };

    return (
      <Card title={<Space><PieChartOutlined /> 充值/售卡分布</Space>} style={{ marginTop: 16 }}>
        <Pie {...pieConfig} height={300} />
      </Card>
    );
  };

  // 渲染渠道统计图表
  const renderChannelStatsChart = () => {
    // 从统计数据中提取渠道数据
    const channelStatsData = Object.entries(statistics.channel_sales || {})
      .filter(([_, value]) => parseFloat(value) > 0) // 过滤掉金额为0的渠道
      .map(([name, value]) => ({
        name,
        value: parseFloat(value)
      }))
      .sort((a, b) => b.value - a.value); // 按金额降序排序

    // 确保数据有效
    if (!channelStatsData || channelStatsData.length === 0) {
      return (
        <Card title={<Space><PieChartOutlined /> 渠道销售统计</Space>} style={{ marginTop: 16 }}>
          <Empty description="暂无渠道销售数据" />
        </Card>
      );
    }

    // 饼图配置
    const pieConfig = {
      data: channelStatsData,
      angleField: 'value',
      colorField: 'name',
      nameField: 'name',
      radius: 0.8,
      label: {
        type: 'outer',
        formatter: (datum) => `${datum.name}: ${(datum.percent * 100).toFixed(2)}%`
      },
      legend: {
        position: 'bottom',
        layout: 'horizontal',
      },
      interactions: [{ type: 'pie-legend-active' }, { type: 'element-active' }],
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `¥${datum.value.toFixed(2)}` };
        },
      },
      statistic: {
        title: {
          style: { fontSize: '14px', color: '#4B535E' },
          content: '渠道销售总额',
        },
        content: {
          style: { fontSize: '20px', color: '#1890ff', fontWeight: 'bold' },
          formatter: () => {
            const total = channelStatsData.reduce((sum, item) => sum + item.value, 0);
            return `¥${total.toFixed(2)}`;
          },
        },
      },
    };

    return (
      <Card title={<Space><PieChartOutlined /> 渠道销售统计</Space>} style={{ marginTop: 16 }}>
        <Pie {...pieConfig} height={300} />
      </Card>
    );
  };

  // 渲染支付方式统计图表
  const renderPaymentStatsChart = () => {
    // 从统计数据中提取支付方式数据
    const paymentStatsData = Object.entries(statistics.payment_methods || {})
      .filter(([_, value]) => parseFloat(value) > 0) // 过滤掉金额为0的支付方式
      .map(([name, value]) => ({
        name,
        value: parseFloat(value)
      }))
      .sort((a, b) => b.value - a.value); // 按金额降序排序

    // 确保数据有效
    if (!paymentStatsData || paymentStatsData.length === 0) {
      return (
        <Card title={<Space><PieChartOutlined /> 支付方式统计</Space>} style={{ marginTop: 16 }}>
          <Empty description="暂无支付方式数据" />
        </Card>
      );
    }

    // 饼图配置
    const pieConfig = {
      data: paymentStatsData,
      angleField: 'value',
      colorField: 'name',
      nameField: 'name',
      radius: 0.8,
      label: {
        type: 'outer',
        formatter: (datum) => `${datum.name}: ${(datum.percent * 100).toFixed(2)}%`
      },
      legend: {
        position: 'bottom',
        layout: 'horizontal',
      },
      interactions: [{ type: 'pie-legend-active' }, { type: 'element-active' }],
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `¥${datum.value.toFixed(2)}` };
        },
      },
      statistic: {
        title: {
          style: { fontSize: '14px', color: '#4B535E' },
          content: '支付总额',
        },
        content: {
          style: { fontSize: '20px', color: '#1890ff', fontWeight: 'bold' },
          formatter: () => {
            const total = paymentStatsData.reduce((sum, item) => sum + item.value, 0);
            return `¥${total.toFixed(2)}`;
          },
        },
      },
    };

    return (
      <Card title={<Space><PieChartOutlined /> 支付方式统计</Space>} style={{ marginTop: 16 }}>
        <Pie {...pieConfig} height={300} />
      </Card>
    );
  };

  return (
    <div className="sales-report-container">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>销售上报</Title>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={async () => {
                // 检查当前选择的日期是否已有班次报表
                const today = dayjs();
                try {
                  const formattedDate = today.format('YYYY-MM-DD');
                  const params = {
                    start_date: formattedDate,
                    end_date: formattedDate,
                    report_type: 'shift'
                  };

                  if (selectedStore) {
                    params.store_id = selectedStore;
                  }

                  const response = await apiService.project.salesReport.getList(params);
                  const hasShiftReports = response && response.items && response.items.length > 0;

                  if (hasShiftReports) {
                    // 如果存在班次报表，提示用户
                    message.warning('当前日期已存在班次报表，建议使用班次报表而不是创建日报。系统将自动根据班次报表汇总计算日销售数据。');
                  }
                } catch (error) {
                  console.error('检查班次报表失败:', error);
                }

                navigate('/project/store/sales-report/create');
              }}
            >
              新建销售上报
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
            >
              导出报告
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Select
                placeholder="选择门店"
                style={{ width: '100%' }}
                allowClear
                onChange={handleStoreChange}
                value={selectedStore}
              >
                {stores.map(store => (
                  <Option key={store.id} value={store.id}>{store.name}</Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <RangePicker
                style={{ width: '100%' }}
                value={dateRange}
                onChange={handleDateRangeChange}
              />
            </Col>
            <Col span={6}>
              <Select
                placeholder="报告类型"
                style={{ width: '100%' }}
                value={reportType}
                onChange={handleReportTypeChange}
              >
                <Option value="all">全部类型</Option>
                <Option value="shift">班次</Option>
                <Option value="daily">日报</Option>
                <Option value="weekly">周报</Option>
                <Option value="monthly">月报</Option>
                <Option value="recharge">充值/售卡</Option>
              </Select>
            </Col>
            <Col span={6}>
              <Input.Search
                placeholder="搜索..."
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
          </Row>
        </div>

        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="销售列表" key="1">
            <Table
              columns={columns}
              dataSource={reports}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>
          <TabPane tab="销售统计" key="2">
            {/* 销售概览 */}
            <Card title="销售概览" style={{ marginBottom: 16, boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
              <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col xs={12} sm={6}>
                  <Card variant="borderless" styles={{ body: { padding: '20px 10px' } }}>
                    <Statistic
                      title="近7日总销售额"
                      value={statistics.total_sales || 0}
                      precision={2}
                      prefix="¥"
                      suffix="元"
                      valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col xs={12} sm={6}>
                  <Card variant="borderless" styles={{ body: { padding: '20px 10px' } }}>
                    <Statistic
                      title="近7日总订单数"
                      value={statistics.total_orders || 0}
                      suffix="笔"
                      valueStyle={{ color: '#52c41a', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col xs={12} sm={6}>
                  <Card variant="borderless" styles={{ body: { padding: '20px 10px' } }}>
                    <Statistic
                      title="近7日充值/售卡总额"
                      value={(statistics.recharge_amount || 0) + (statistics.card_sales_amount || 0)}
                      precision={2}
                      prefix="¥"
                      suffix="元"
                      valueStyle={{ color: '#fa8c16', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col xs={12} sm={6}>
                  <Card variant="borderless" styles={{ body: { padding: '20px 10px' } }}>
                    <Statistic
                      title="客单价"
                      value={statistics.total_orders > 0 ? (statistics.total_sales / statistics.total_orders) : 0}
                      precision={2}
                      prefix="¥"
                      suffix="元"
                      valueStyle={{ color: '#722ed1', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col xs={12} sm={6}>
                  <Card bordered={false} bodyStyle={{ padding: '20px 10px' }}>
                    <Statistic
                      title="今日销售额"
                      value={statistics.today_sales || 0}
                      precision={2}
                      prefix="¥"
                      suffix="元"
                      valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col xs={12} sm={6}>
                  <Card bordered={false} bodyStyle={{ padding: '20px 10px' }}>
                    <Statistic
                      title="今日订单数"
                      value={statistics.today_orders || 0}
                      suffix="笔"
                      valueStyle={{ color: '#52c41a', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col xs={12} sm={6}>
                  <Card bordered={false} bodyStyle={{ padding: '20px 10px' }}>
                    <Statistic
                      title="今日客流量"
                      value={statistics.today_customers || 0}
                      suffix="人"
                      valueStyle={{ color: '#fa8c16', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col xs={12} sm={6}>
                  <Card bordered={false} bodyStyle={{ padding: '20px 10px' }}>
                    <Statistic
                      title="今日客单价"
                      value={statistics.today_orders > 0 ? (statistics.today_sales / statistics.today_orders) : 0}
                      precision={2}
                      prefix="¥"
                      suffix="元"
                      valueStyle={{ color: '#722ed1', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
              </Row>
            </Card>

            {/* 销售趋势图 */}
            <Card title="销售趋势" style={{ marginBottom: 16 }}>
              {channelData.length > 0 ? (
                <Column
                  data={channelData
                    .filter(item => item && item.name && item.value)
                    .map(item => ({
                      date: item.name.length > 20 ? '未知渠道' : item.name,
                      value: item.value
                    }))}
                  xField="date"
                  yField="value"
                  meta={{
                    value: {
                      formatter: (value) => value ? `¥${parseFloat(value).toFixed(2)}` : '¥0.00'
                    }
                  }}
                  label={{
                    position: 'top',
                    formatter: (datum) => {
                      if (!datum || typeof datum.value === 'undefined') return '¥0';
                      return `¥${parseFloat(datum.value).toFixed(0)}`;
                    }
                  }}
                  tooltip={{
                    formatter: (datum) => {
                      if (!datum || typeof datum.value === 'undefined') {
                        return { name: '销售额', value: '¥0.00' };
                      }
                      return {
                        name: '销售额',
                        value: `¥${parseFloat(datum.value).toFixed(2)}`
                      };
                    }
                  }}
                  height={300}
                />
              ) : (
                <Empty description="暂无销售趋势数据" />
              )}
            </Card>

            {/* 渠道销售和支付方式 */}
            <Row gutter={16}>
              <Col span={12}>
                <Card title="渠道销售分布" style={{ marginBottom: 16 }}>
                  {channelData && channelData.length > 0 ? (
                    <Pie
                      data={channelData.map(item => ({
                        ...item,
                        name: item.name.length > 20 ? '未知渠道' : item.name
                      }))}
                      angleField="value"
                      colorField="name"
                      radius={0.8}
                      label={{
                        // 移除 type: 'outer'，使用默认标签类型
                        formatter: (datum) => {
                          if (!datum || typeof datum.name === 'undefined' || typeof datum.percent === 'undefined') {
                            return '';
                          }
                          return `${datum.name || '未知'}: ${((datum.percent || 0) * 100).toFixed(1)}%`;
                        }
                      }}
                      tooltip={{
                        formatter: (datum) => {
                          if (!datum || typeof datum.name === 'undefined' || typeof datum.value === 'undefined') {
                            return { name: '未知', value: '¥0.00' };
                          }
                          return {
                            name: datum.name || '未知',
                            value: `¥${parseFloat(datum.value || 0).toFixed(2)}`
                          };
                        }
                      }}
                      height={300}
                    />
                  ) : (
                    <Empty description="暂无渠道销售数据" />
                  )}
                </Card>
              </Col>
              <Col span={12}>
                <Card title="支付方式分布" style={{ marginBottom: 16 }}>
                  {Object.keys(statistics.payment_methods || {}).length > 0 ? (
                    <Pie
                      data={Object.entries(statistics.payment_methods || {})
                        .filter(([_, value]) => parseFloat(value) > 0)
                        .map(([name, value]) => ({
                          name: name.length > 20 ? '未知支付方式' : name,
                          value: parseFloat(value)
                        }))}
                      angleField="value"
                      colorField="name"
                      radius={0.8}
                      label={{
                        // 移除 type: 'outer'，使用默认标签类型
                        formatter: (datum) => {
                          if (!datum || typeof datum.name === 'undefined' || typeof datum.percent === 'undefined') {
                            return '';
                          }
                          return `${datum.name || '未知'}: ${((datum.percent || 0) * 100).toFixed(1)}%`;
                        }
                      }}
                      tooltip={{
                        formatter: (datum) => {
                          if (!datum || typeof datum.name === 'undefined' || typeof datum.value === 'undefined') {
                            return { name: '未知', value: '¥0.00' };
                          }
                          return {
                            name: datum.name || '未知',
                            value: `¥${parseFloat(datum.value || 0).toFixed(2)}`
                          };
                        }
                      }}
                      height={300}
                    />
                  ) : (
                    <Empty description="暂无支付方式数据" />
                  )}
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane tab="销售月报" key="3">
            <SalesMonthlyReport
              selectedStore={selectedStore}
              dateRange={dateRange}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default SalesReport;
